-- Do<PERSON><PERSON><PERSON> teenuse spetsiifiline andmebaas
-- <PERSON><PERSON> hoitakse nii üldised kui ka teenuse-spetsiifilised andmed

CREATE TABLE IF NOT EXISTS dokument (
    id INT AUTO_INCREMENT PRIMARY KEY,

    -- <PERSON>ld<PERSON> kliendi andmed (dubleeritud alimendid_kliendid andmebaasist)
    klient_nimi VARCHAR(255) NOT NULL,
    teenus VARCHAR(100) NOT NULL DEFAULT 'Dokument',
    hind DECIMAL(10,2) NOT NULL,
    klient_email VARCHAR(255) NOT NULL,
    klient_telefon VARCHAR(50),
    pakett VARCHAR(50) DEFAULT 'dokument-odav',
    klient_roll ENUM('klient', 'admin') DEFAULT 'klient',
    tellimuse_kuupaev TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    staatus ENUM('ootel', 'kinnitatud', 'tootlemisel', 'valmis', 'tühistatud') DEFAULT 'ootel',

    -- Do<PERSON><PERSON><PERSON> teenuse spetsiifilised väljad
    klient_lisainfo TEXT,
    klient_dokumendid TEXT, -- JSON formaat failide jaoks
    klient_kasutustingimused TINYINT(1) DEFAULT 0,

    -- Lapse andmed (JSON formaat)
    lapsed_andmed TEXT,

    -- Teise vanema andmed
    teine_vanem_nimi VARCHAR(255),
    teine_vanem_isikukood VARCHAR(20),
    teine_vanem_aadress TEXT,
    teine_vanem_telefon VARCHAR(50),
    teine_vanem_email VARCHAR(255),

    -- Elatise andmed
    elatise_liik ENUM('uus_elatis', 'elatise_suurendamine', 'elatisvola_noudmine', 'elatisabi_taotlus') DEFAULT 'uus_elatis',
    elatise_summa DECIMAL(10,2),
    elatise_alus TEXT,
    elatise_pohjendus TEXT,

    -- Sissetulekute andmed
    klient_sissetulek DECIMAL(10,2),
    klient_sissetuleku_allikas VARCHAR(255),
    teine_vanem_sissetulek DECIMAL(10,2),
    teine_vanem_sissetuleku_allikas VARCHAR(255),

    -- Kulutuste andmed
    lapse_kulutused DECIMAL(10,2),
    kulutuste_kirjeldus TEXT,

    -- Elamise andmed
    lapse_elamise_koht ENUM('kliendi_juures', 'teise_vanema_juures', 'vaheldumisi') DEFAULT 'kliendi_juures',
    hoolduspaevade_arv INT DEFAULT 0,

    -- Varasemad kohtulahendid
    varasem_kohtulahend TINYINT(1) DEFAULT 0,
    kohtulahendi_kuupaev DATE NULL,
    kohtulahendi_sisu TEXT,

    -- Metaandmed
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- Indeksid
    INDEX idx_klient_email (klient_email),
    INDEX idx_tellimuse_kuupaev (tellimuse_kuupaev),
    INDEX idx_staatus (staatus),
    INDEX idx_pakett (pakett)
);

-- Kommentaarid tabelile
ALTER TABLE dokument COMMENT = 'Dokumendi teenuse klientide andmed ja tellimused';

-- Näidisandmed (testimiseks)
INSERT INTO dokument (
    klient_nimi, 
    klient_email, 
    klient_telefon, 
    teenus, 
    pakett, 
    hind,
    klient_roll,
    staatus,
    klient_lisainfo,
    klient_kasutustingimused,
    lapsed_andmed,
    teine_vanem_nimi,
    teine_vanem_isikukood,
    elatise_liik,
    elatise_summa,
    klient_sissetulek,
    lapse_kulutused
) VALUES (
    'Test Klient', 
    '<EMAIL>', 
    '+372 5555 5555', 
    'Dokument', 
    'dokument-odav', 
    69.00,
    'klient',
    'ootel',
    'Test kirjeldus dokumendi teenuse jaoks',
    1,
    '[{"nimi":"Test Laps","isikukood":"51234567890","paevade_arv":30}]',
    'Test Teine Vanem',
    '41234567890',
    'uus_elatis',
    200.00,
    1500.00,
    300.00
);
