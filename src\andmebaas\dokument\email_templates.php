<?php
// Dokumendi e-posti mallid

require_once __DIR__ . '/../email_functions.php';

// Funktsioon dokumendi kinnituskirja saatmiseks
function send_dokument_confirmation($klient_nimi, $klient_email, $pakett, $hind) {
    
    // E-kirja teema
    $subject = 'Dokumendi tellimuse kinnitus - Alimendid.ee';
    
    // Paketi kirjeldused
    $paketi_kirjeldused = [
        'dokument-odav' => 'Elatise dokument (odav pakett)',
        'dokument-keskmine' => 'Elatise dokument (keskmine pakett)', 
        'dokument-kallis' => 'Elatise dokument (kallis pakett)'
    ];
    
    $paketi_kirjeldus = $paketi_kirjeldused[$pakett] ?? 'Elatise dokument';
    
    // Valmimisajad paketi põhjal
    $valmimisajad = [
        'dokument-odav' => '5 tööpäeva',
        'dokument-keskmine' => '3 tööpäeva',
        'dokument-kallis' => '3 tööpäeva'
    ];
    
    $valmimisaeg = $valmimisajad[$pakett] ?? '5 tööpäeva';
    
    // E-kirja sisu
    $content = '
        <h2>Tere, ' . htmlspecialchars($klient_nimi) . '!</h2>
        
        <p>Täname teid dokumendi tellimuse eest. Teie taotlus on edukalt vastu võetud.</p>
        
        <div class="highlight">
            <h3>Tellimuse andmed:</h3>
            <p><strong>Klient:</strong> ' . htmlspecialchars($klient_nimi) . '</p>
            <p><strong>Teenus:</strong> ' . htmlspecialchars($paketi_kirjeldus) . '</p>
            <p><strong>Hind:</strong> ' . htmlspecialchars($hind) . '€</p>
            <p><strong>Valmimisaeg:</strong> ' . htmlspecialchars($valmimisaeg) . '</p>
        </div>
        
        <h3>Järgmised sammud:</h3>
        <ol>
            <li><strong>Makse sooritamine:</strong> Palun tasuge teenustasu pangalingi kaudu</li>
            <li><strong>Dokumendi koostamine:</strong> Alustame dokumendi koostamist ühe tööpäeva jooksul pärast makse laekumist</li>
            <li><strong>Valmis dokument:</strong> Saadame valmis dokumendi teile e-kirja teel</li>
        </ol>
        
        <p>Vajadusel võtame teiega ühendust, et täpsustada asjaolusid või küsida lisainfot.</p>
        
        <h3>Mida dokument sisaldab?</h3>
        <ul>
            <li>Juriidiliselt korrektne elatise dokument</li>
            <li>Põhjalik asjaolude kirjeldus</li>
            <li>Viited seadustele ja kohtupraktikale</li>
            <li>Juristi soovitused edasiseks tegutsemiseks</li>
        </ul>
        
        <p>Kui teil on küsimusi, võtke julgelt ühendust.</p>
        
        <p>Lugupidamisega,<br>
        <strong>Alimendid.ee meeskond</strong></p>
    ';
    
    // Loo HTML e-kiri
    $html_body = create_html_email('Dokumendi tellimuse kinnitus', $content);
    
    // Loo teksti versioon
    $text_body = html_to_text($html_body);
    
    // Saada e-kiri kliendile
    $client_result = send_email($klient_email, $klient_nimi, $subject, $html_body, $text_body);

    // Saada koopia administraatorile
    $config = get_email_config();
    if (isset($config['admin_email']) && !empty($config['admin_email'])) {
        $admin_subject = 'UUS DOKUMENT: ' . $klient_nimi;
        $admin_content = '
            <h2>Uus dokumendi taotlus</h2>
            <div class="highlight">
                <p><strong>Klient:</strong> ' . htmlspecialchars($klient_nimi) . '</p>
                <p><strong>E-post:</strong> ' . htmlspecialchars($klient_email) . '</p>
                <p><strong>Pakett:</strong> ' . htmlspecialchars($paketi_kirjeldus) . '</p>
                <p><strong>Hind:</strong> ' . htmlspecialchars($hind) . '€</p>
                <p><strong>Valmimisaeg:</strong> ' . htmlspecialchars($valmimisaeg) . '</p>
            </div>
            <p>Palun alustage dokumendi koostamist pärast makse laekumist.</p>
        ';
        $admin_html = create_html_email('Uus dokumendi tellimus', $admin_content);
        send_email($config['admin_email'], 'Administraator', $admin_subject, $admin_html);
    }

    return $client_result;
}

// Funktsioon dokumendi valmimise teatise saatmiseks (tulevikus kasutamiseks)
function send_dokument_ready($klient_nimi, $klient_email, $pakett) {
    
    $subject = 'Dokument on valmis - Alimendid.ee';
    
    $paketi_kirjeldused = [
        'dokument-odav' => 'Elatise dokument (odav pakett)',
        'dokument-keskmine' => 'Elatise dokument (keskmine pakett)', 
        'dokument-kallis' => 'Elatise dokument (kallis pakett)'
    ];
    
    $paketi_kirjeldus = $paketi_kirjeldused[$pakett] ?? 'Elatise dokument';
    
    $content = '
        <h2>Tere, ' . htmlspecialchars($klient_nimi) . '!</h2>
        
        <p>Teie tellitud dokument on valmis!</p>
        
        <div class="highlight">
            <h3>Valmis dokument:</h3>
            <p><strong>Teenus:</strong> ' . htmlspecialchars($paketi_kirjeldus) . '</p>
            <p><strong>Staatus:</strong> Valmis</p>
        </div>
        
        <p>Dokument on manuses. Palun tutvuge dokumendiga põhjalikult.</p>
        
        <h3>Kuidas edasi toimida?</h3>
        <ul>
            <li>Tutvuge dokumendi sisuga</li>
            <li>Vajadusel võtke ühendust teise vanemaga</li>
            <li>Kui kokkulepet ei saavutata, esitage dokument kohtule</li>
            <li>Järgige dokumendis toodud juhiseid</li>
        </ul>
        
        <p>Kui teil on küsimusi dokumendi kohta, võtke julgelt ühendust.</p>
        
        <p>Soovime edu elatise küsimuse lahendamisel!</p>
        
        <p>Lugupidamisega,<br>
        <strong>Alimendid.ee meeskond</strong></p>
    ';
    
    $html_body = create_html_email('Dokument on valmis', $content);
    $text_body = html_to_text($html_body);
    
    return send_email($klient_email, $klient_nimi, $subject, $html_body, $text_body);
}

// Funktsioon dokumendi täiendamise vajaduse teatise saatmiseks (tulevikus kasutamiseks)
function send_dokument_additional_info_needed($klient_nimi, $klient_email, $vajaminev_info) {
    
    $subject = 'Vajame lisainfot dokumendi koostamiseks - Alimendid.ee';
    
    $content = '
        <h2>Tere, ' . htmlspecialchars($klient_nimi) . '!</h2>
        
        <p>Dokumendi koostamise käigus selgus, et vajame teilt lisainfot.</p>
        
        <div class="highlight">
            <h3>Vajaminev informatsioon:</h3>
            <p>' . nl2br(htmlspecialchars($vajaminev_info)) . '</p>
        </div>
        
        <p>Palun edastage vajalik informatsioon võimalikult kiiresti, et saaksime dokumendi koostamist jätkata.</p>
        
        <p>Võite vastata sellele e-kirjale või võtta meiega ühendust telefoni teel.</p>
        
        <p>Aitäh koostöö eest!</p>
        
        <p>Lugupidamisega,<br>
        <strong>Alimendid.ee meeskond</strong></p>
    ';
    
    $html_body = create_html_email('Vajame lisainfot', $content);
    $text_body = html_to_text($html_body);
    
    return send_email($klient_email, $klient_nimi, $subject, $html_body, $text_body);
}

// Funktsioon dokumendi tellimuse tühistamise teatise saatmiseks (tulevikus kasutamiseks)
function send_dokument_cancellation($klient_nimi, $klient_email, $pohjus = '') {
    
    $subject = 'Dokumendi tellimuse tühistamine - Alimendid.ee';
    
    $content = '
        <h2>Tere, ' . htmlspecialchars($klient_nimi) . '!</h2>
        
        <p>Kahjuks pidime teie dokumendi tellimuse tühistama.</p>
        
        ' . (!empty($pohjus) ? '<div class="highlight">
            <h3>Tühistamise põhjus:</h3>
            <p>' . nl2br(htmlspecialchars($pohjus)) . '</p>
        </div>' : '') . '
        
        <p>Kui soovite teenust uuesti tellida või teil on küsimusi, võtke julgelt ühendust.</p>
        
        <p>Vabandame ebamugavuste pärast.</p>
        
        <p>Lugupidamisega,<br>
        <strong>Alimendid.ee meeskond</strong></p>
    ';
    
    $html_body = create_html_email('Dokumendi tellimuse tühistamine', $content);
    $text_body = html_to_text($html_body);
    
    return send_email($klient_email, $klient_nimi, $subject, $html_body, $text_body);
}
?>
