    <!-- Alimendid.ee andmebaas -->
    <?php
    require_once '../alimendid-teenused.php';
    require_once '../../../andmebaas/dokument/dokument_db.php';
    ?>

    <?php
    // Muutujad andmete kuvamiseks
    $klient_nimi = '';
    $pakett = '';
    $hind = '';
    $save_result = ['success' => true]; // Eeldame, et jõudsime siia edukalt
    $elatise_id = null;

    // Kontrolli, kas ID on GET parameetris (suunatud andmed.php-st)
    if (isset($_GET['id'])) {
        $elatise_id = (int)$_GET['id'];

        // Loe andmed andmebaasist
        $data = get_dokument_data($elatise_id);
        if ($data) {
            $klient_nimi = $data['klient_nimi'];
            $pakett = $data['pakett'];
            $hind = $data['hind'];
        } else {
            $save_result = ['success' => false, 'message' => 'Andmeid ei leitud'];
        }
    } else {
        $save_result = ['success' => false, 'message' => 'Puudub tellimuse ID'];
    }
    ?>


    <!DOCTYPE html>
    <html lang="et" class="relative min-h-full">

    <head>
        <meta charset="utf-8">
        <meta name="robots" content="index, follow">
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
        <meta name="description" content="Juristi koostatud elatise dokument">
        <link rel="canonical" href="https://alimendid.ee/et/teenused/dokument/kinnitus.php">
        <meta property="og:type" content="website">
        <meta property="og:url" content="https://alimendid.ee/assets/failid/pildid/meta/alimendid.jpg">
        <meta property="og:title" content="Dokument - Kinnitus · Alimendid.ee">
        <meta property="og:description" content="Juristi koostatud elatise dokument">
        <meta property="og:image" content="https://alimendid.ee/assets/failid/pildid/meta/alimendid.jpg">
        <meta property="twitter:card" content="summary_large_image">
        <meta property="twitter:url" content="https://alimendid.ee/et/teenused/dokument/">
        <meta property="twitter:title" content="Dokument - Kinnitus · Alimendid.ee">
        <meta property="twitter:description" content="Juristi koostatud elatise dokument">
        <meta property="twitter:image" content="https://alimendid.ee/assets/failid/pildid/meta/alimendid.jpg">

        <title>Dokument - Kinnitus · Alimendid.ee</title>

        <!-- Favicon -->
        <link rel="shortcut icon" href="../../../assets/failid/favicon/favicon.ico">

        <!-- Font -->
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

        <!-- CSS HS -->
        <link href="../../../output.css" rel="stylesheet" />
        <link rel="stylesheet" href="../../../assets/css/main.min.css">

        <!-- Theme Check and Update -->
        <script>
            const html = document.querySelector('html');
            const isLightOrAuto = localStorage.getItem('hs_theme') === 'light' || (localStorage.getItem('hs_theme') === 'auto' && !window.matchMedia('(prefers-color-scheme: dark)').matches);
            const isDarkOrAuto = localStorage.getItem('hs_theme') === 'dark' || (localStorage.getItem('hs_theme') === 'auto' && window.matchMedia('(prefers-color-scheme: dark)').matches);

            if (isLightOrAuto && html.classList.contains('dark')) html.classList.remove('dark');
            else if (isDarkOrAuto && html.classList.contains('light')) html.classList.remove('light');
            else if (isDarkOrAuto && !html.classList.contains('dark')) html.classList.add('dark');
            else if (isLightOrAuto && !html.classList.contains('light')) html.classList.add('light');
        </script>

        <!-- Google Ads-->
        <script>
            window.dataLayer = window.dataLayer || [];

            function gtag() {
                dataLayer.push(arguments);
            }
            gtag('js', new Date());

            gtag('config', 'UA-221277240-1');
        </script>

        <!-- Google tag (gtag.js) -->
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-C8JJCED3EQ"></script>
        <script>
            window.dataLayer = window.dataLayer || [];

            function gtag() {
                dataLayer.push(arguments);
            }
            gtag('js', new Date());

            gtag('config', 'G-C8JJCED3EQ');
        </script>

        <!-- Google Tag Manager -->
        <script>
            (function(w, d, s, l, i) {
                w[l] = w[l] || [];
                w[l].push({
                    'gtm.start': new Date().getTime(),
                    event: 'gtm.js'
                });
                var f = d.getElementsByTagName(s)[0],
                    j = d.createElement(s),
                    dl = l != 'dataLayer' ? '&l=' + l : '';
                j.async = true;
                j.src =
                    'https://www.googletagmanager.com/gtm.js?id=' + i + dl;
                f.parentNode.insertBefore(j, f);
            })(window, document, 'script', 'dataLayer', 'GTM-T554HTP');
        </script>

        <!-- Hotjar -->
        <script>
            (function(h, o, t, j, a, r) {
                h.hj = h.hj || function() {
                    (h.hj.q = h.hj.q || []).push(arguments)
                };
                h._hjSettings = {
                    hjid: 3283746,
                    hjsv: 6
                };
                a = o.getElementsByTagName('head')[0];
                r = o.createElement('script');
                r.async = 1;
                r.src = t + h._hjSettings.hjid + j + h._hjSettings.hjsv;
                a.appendChild(r);
            })(window, document, 'https://static.hotjar.com/c/hotjar-', '.js?sv=');
        </script>
    </head>

    <body class="dark:bg-neutral-900">
        <!-- Google Tag Manager (noscript) -->
        <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-T554HTP" height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>

        <!-- ========== HEADER ========== -->
        <?php include '../../../assets/failid/komponendid/et/menu.php'; ?>
        <!-- ========== END HEADER ========== -->

        <!-- ========== MAIN CONTENT ========== -->
        <main id="content">

            <!-- //SECTION - Hero -->
            <div class="max-w-2xl px-4 sm:px-6 lg:px-8 py-12 lg:py-24 mx-auto">

                <!-- //LINK - Pealkiri -->
                <div class="max-w-2xl text-center mx-auto">
                    <h1 class="font-medium text-black text-3xl sm:text-4xl dark:text-white mb-2">
                        Elatise dokument
                    </h1>
                    <p class="text-gray-600 dark:text-gray-300">
                        Juristi koostatud elatise dokument
                    </p>
                </div>

                <div class="lg:max-w-xl mx-auto">
                    <div class="bg-white dark:bg-neutral-800 p-4">

                        <div class="prose prose-gray max-w-none dark:prose-invert">

                            <?php if ($save_result['success']): ?>
                                <!-- Edukas salvestamine -->
                                <div class="last:pb-0 last:mb-0 last:border-b-0 dark:border-neutral-700">

                                    <!-- //LINK - Ikoon -->
                                    <div class="flex justify-center mb-6">
                                        <div class="flex justify-center items-center size-16 bg-green-100 rounded-full dark:bg-green-800/30">
                                            <svg class="shrink-0 size-6 text-green-600 dark:text-green-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z" />
                                                <path d="m9 12 2 2 4-4" />
                                            </svg>
                                        </div>
                                    </div>

                                    <h2 class="text-2xl font-semibold text-gray-800 dark:text-neutral-200 text-center mb-4">
                                        Aitäh! Andmed on edukalt vastu võetud.
                                    </h2>

                                    <p class="text-gray-600 dark:text-gray-300 mb-4 text-center">
                                        Tellimus on edukalt esitatud. Alustame dokumendi koostamist ühe tööpäeva jooksul.
                                    </p>

                                    <!-- Tellimuse andmed -->
                                    <div class="bg-gray-50 dark:bg-neutral-700 p-4 rounded-lg mb-6">
                                        <h3 class="font-semibold text-gray-800 dark:text-neutral-200 mb-3">Tellimuse andmed:</h3>
                                        <div class="space-y-2">
                                            <div class="flex justify-between">
                                                <span class="text-gray-600 dark:text-gray-300">Klient:</span>
                                                <span class="font-medium text-gray-800 dark:text-neutral-200"><?php echo htmlspecialchars($klient_nimi); ?></span>
                                            </div>
                                            <div class="flex justify-between">
                                                <span class="text-gray-600 dark:text-gray-300">Teenus:</span>
                                                <span class="font-medium text-gray-800 dark:text-neutral-200">Elatise dokument</span>
                                            </div>
                                            <div class="flex justify-between">
                                                <span class="text-gray-600 dark:text-gray-300">Pakett:</span>
                                                <span class="font-medium text-gray-800 dark:text-neutral-200"><?php echo htmlspecialchars($pakett); ?></span>
                                            </div>
                                            <div class="flex justify-between">
                                                <span class="text-gray-600 dark:text-gray-300">Hind:</span>
                                                <span class="font-medium text-gray-800 dark:text-neutral-200"><?php echo htmlspecialchars($hind); ?>€</span>
                                            </div>
                                        </div>
                                    </div>

                                    <p class="text-gray-600 dark:text-gray-300 mb-6 text-center">
                                        Saatsime Teile kinnituskirja e-posti aadressile. Dokument valmib 1-5 tööpäeva jooksul sõltuvalt valitud paketist.
                                    </p>

                                </div>

                                <div class="max-w-xl mx-auto">
                                    <div class="max-w-xs mx-auto">
                                        <div class="mb-10 flex items-center justify-center">
                                            <a href="<?php echo $alimendid_teenus['dokument'][$pakett]['hind_makselink'] ?? '#'; ?>" class="w-64 py-2.5 px-3.5 inline-flex justify-center items-center gap-x-2 font-medium text-nowrap rounded-xl border border-transparent bg-orange-500 text-white hover:bg-orange-600 focus:outline-hidden focus:bg-orange-600 transition disabled:opacity-50 disabled:pointer-events-none">
                                                <span class="font-semibold text-white">Maksa <?php echo htmlspecialchars($hind); ?>€</span>
                                            </a>
                                        </div>
                                    </div>
                                </div>

                            <?php else: ?>
                                <!-- Viga -->
                                <div class="last:pb-0 last:mb-0 last:border-b-0 dark:border-neutral-700">

                                    <!-- //LINK - Ikoon -->
                                    <div class="flex justify-center mb-6">
                                        <div class="flex justify-center items-center size-16 bg-red-100 rounded-full dark:bg-red-800/30">
                                            <svg class="shrink-0 size-6 text-red-600 dark:text-red-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <circle cx="12" cy="12" r="10" />
                                                <path d="m15 9-6 6" />
                                                <path d="m9 9 6 6" />
                                            </svg>
                                        </div>
                                    </div>

                                    <h2 class="text-2xl font-semibold text-gray-800 dark:text-neutral-200 text-center mb-4">
                                        Viga andmete laadimisel
                                    </h2>

                                    <p class="text-gray-600 dark:text-gray-300 mb-6 text-center">
                                        <?php echo htmlspecialchars($save_result['message'] ?? 'Tekkis viga andmete laadimisel.'); ?>
                                    </p>

                                    <div class="flex justify-center">
                                        <a href="andmed.php" class="py-3 px-4 inline-flex justify-center items-center gap-x-2 text-md font-semibold rounded-lg border border-transparent bg-orange-500 text-white hover:bg-orange-600 focus:outline-hidden focus:bg-orange-600 transition disabled:opacity-50 disabled:pointer-events-none">
                                            Proovi uuesti
                                        </a>
                                    </div>

                                </div>
                            <?php endif; ?>

                        </div>

                    </div>
                </div>

            </div>

        </main>
        <!-- ========== END MAIN CONTENT ========== -->

        <!-- //LINK - Footer -->
        <?php include '../../../assets/failid/komponendid/et/footer.php'; ?>

        <!-- JS PLUGINS -->
        <!-- Required plugins -->
        <script src="../../../assets/vendor/lodash/lodash.min.js"></script>
        <script src="../../../assets/vendor/preline/dist/index.js?v=3.0.1"></script>
        <!-- Clipboard -->
        <script src="../../../assets/vendor/clipboard/dist/clipboard.min.js"></script>
        <script src="../../../assets/js/hs-copy-clipboard-helper.js"></script>

    </body>

    </html>