<!DOCTYPE html>
<html lang="et" class="relative min-h-full">

<head>
    <meta charset="utf-8"> <!-- Märgistik: UTF-8 kodeering -->
    <meta name="robots" content="index, follow"> <!-- Otsingumootoritele: indekseeri ja järgi linke -->
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"> <!-- Mobiilisõbralik vaade -->
    <meta name="description" content="Juriidiliselt korrektne elatise dokument esitamiseks kohtule või teisele vanemale."> <!-- Meta-kirjeldus otsingutulemustes -->
    <link rel="canonical" href="https://alimendid.ee/et/teenused/elatise-dokument/alusta.php"> <!-- Eelistatud URL, väldib duplikaati -->
    <meta property="og:type" content="website"> <!-- Open Graph: sisutüüp on veebileht -->
    <meta property="og:url" content="https://alimendid.ee/assets/failid/pildid/meta/alimendid.jpg"> <!-- OG: jagamise URL -->
    <meta property="og:title" content="Elatise dokument · Alimendid.ee"> <!-- OG: pealkiri jagamisel -->
    <meta property="og:description" content="Juriidiliselt korrektne elatise dokument esitamiseks kohtule või teisele vanemale."> <!-- OG: jagamise kirjeldus -->
    <meta property="og:image" content="https://alimendid.ee/assets/failid/pildid/meta/alimendid.jpg"> <!-- OG: jagamise pilt -->
    <meta property="twitter:card" content="summary_large_image"> <!-- Twitter: suur jagamispilt -->
    <meta property="twitter:url" content="https://alimendid.ee/et/teenused/elatise-dokument/"> <!-- Twitter: jagamise URL -->
    <meta property="twitter:title" content="Elatise dokument · Alimendid.ee"> <!-- Twitter: pealkiri -->
    <meta property="twitter:description" content="Juriidiliselt korrektne elatise dokument esitamiseks kohtule või teisele vanemale."> <!-- Twitter: kirjeldus -->
    <meta property="twitter:image" content="https://alimendid.ee/assets/failid/pildid/meta/alimendid.jpg"> <!-- Twitter: jagamise pilt -->

    <title>Elatise dokument · Alimendid.ee</title> <!-- Lehe pealkiri brauseris ja otsingus -->

    <!-- Favicon -->
    <link rel="shortcut icon" href="../../../assets/img/favicon.ico">

    <!-- Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- CSS HS -->

        <!-- CSS HS -->
        <link href="../../../output.css" rel="stylesheet" />
        <link href="../../../assets/css/main.min.css" rel="stylesheet" />

    <!-- Theme Check and Update -->
    <script>
        const html = document.querySelector('html');
        const isLightOrAuto = localStorage.getItem('hs_theme') === 'light' || (localStorage.getItem('hs_theme') === 'auto' && !window.matchMedia('(prefers-color-scheme: dark)').matches);
        const isDarkOrAuto = localStorage.getItem('hs_theme') === 'dark' || (localStorage.getItem('hs_theme') === 'auto' && window.matchMedia('(prefers-color-scheme: dark)').matches);

        if (isLightOrAuto && html.classList.contains('dark')) html.classList.remove('dark');
        else if (isDarkOrAuto && html.classList.contains('light')) html.classList.remove('light');
        else if (isDarkOrAuto && !html.classList.contains('dark')) html.classList.add('dark');
        else if (isLightOrAuto && !html.classList.contains('light')) html.classList.add('light');
    </script>


    <!-- Google Ads-->
    <script>
        window.dataLayer = window.dataLayer || [];

        function gtag() {
            dataLayer.push(arguments);
        }
        gtag('js', new Date());

        gtag('config', 'UA-221277240-1');
    </script>

    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-C8JJCED3EQ"></script>
    <script>
        window.dataLayer = window.dataLayer || [];

        function gtag() {
            dataLayer.push(arguments);
        }
        gtag('js', new Date());

        gtag('config', 'G-C8JJCED3EQ');
    </script>

    <!-- Google Tag Manager -->
    <script>
        (function(w, d, s, l, i) {
            w[l] = w[l] || [];
            w[l].push({
                'gtm.start': new Date().getTime(),
                event: 'gtm.js'
            });
            var f = d.getElementsByTagName(s)[0],
                j = d.createElement(s),
                dl = l != 'dataLayer' ? '&l=' + l : '';
            j.async = true;
            j.src =
                'https://www.googletagmanager.com/gtm.js?id=' + i + dl;
            f.parentNode.insertBefore(j, f);
        })(window, document, 'script', 'dataLayer', 'GTM-T554HTP');
    </script>

    <!-- Hotjar -->
    <script>
        (function(h, o, t, j, a, r) {
            h.hj = h.hj || function() {
                (h.hj.q = h.hj.q || []).push(arguments)
            };
            h._hjSettings = {
                hjid: 3283746,
                hjsv: 6
            };
            a = o.getElementsByTagName('head')[0];
            r = o.createElement('script');
            r.async = 1;
            r.src = t + h._hjSettings.hjid + j + h._hjSettings.hjsv;
            a.appendChild(r);
        })(window, document, 'https://static.hotjar.com/c/hotjar-', '.js?sv=');
    </script>
</head>

<body class="dark:bg-neutral-900">
    <!-- Google Tag Manager -->
    <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-T554HTP" height="0" width="0"
            style="display:none;visibility:hidden"></iframe></noscript>
    <!-- ========== HEADER ========== -->
    <header class="">

        <!-- Google Tag Manager (noscript) -->
        <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-T554HTP" height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>

        <?php include '../../../assets/failid/komponendid/et/menu.php'; ?>
    </header>
    <!-- ========== END HEADER ========== -->

    <!-- ========== MAIN CONTENT ========== -->
    <main id="content">
        <div class="max-w-2xl px-4 sm:px-6 lg:px-8 py-12 lg:py-24 mx-auto">

            <!-- LINK ‧ Pealkiri -->
            <div class="max-w-2xl text-center mx-auto">
                <h1 class="font-medium text-black text-3xl sm:text-4xl dark:text-white mb-2">
                    Elatise dokument
                </h1>
                <p class="text-gray-600 dark:text-gray-300">
                   Juristi koostatud elatise dokument
                </p>
            </div>

            <div class="lg:max-w-xl mx-auto">
                <div class="bg-white dark:bg-neutral-800 p-4">

                    <div class="prose prose-gray max-w-none dark:prose-invert">

                        <!-- LINK ‧ Selgitus -->
                        <div class="last:pb-0 last:mb-0 last:border-b-0 dark:border-neutral-700">
                            <p class="text-gray-600 dark:text-gray-300 mb-4">
                                Koostame Sinu vajadustele vastava juriidiliselt korrektse <span class="font-semibold text-orange-500">elatise dokumendi</span>, mida saad kasutada teise vanema poole pöördumiseks või kohtule esitamiseks.
                            </p>

                            <p class="text-gray-600 dark:text-gray-300 mb-6">
                                Dokumendi saamiseks sisesta andmed ja vasta küsimustele. Seejärel suunatakse Sind makselehele, kus saad teenustasu <span class="font-semibold text-orange-500"><?php echo "69"; ?>€</span> tasuda pangalingi kaudu.

                            </p>
                            <p class="text-gray-600 dark:text-gray-300 mb-6">
                                Makse laekumise järel alustame dokumendi koostamist. Valmis dokumendi saadame Sulle e-kirja teel hiljemalt <span class="font-semibold text-orange-500"><?php echo date('d.m.Y', strtotime('+5 days')); ?></span>. Vajadusel võtame Sinuga ühendust, et täpsustada asjaolusid.
                            </p>
                        </div>

                        <!-- LINK ‧ NUPUD -->
                        <div class="flex flex-col gap-y-3 pt-6">
                            <button onclick="window.location.href='andmed.php'"
                                class="py-3 px-3 w-full inline-flex justify-center items-center gap-x-1.5 font-medium rounded-xl bg-orange-500 text-lg text-white focus:outline-none transition">
                                Alusta siit
                            </button>

                            <a class="py-3 px-3 w-full inline-flex justify-center items-center gap-x-1.5 font-medium rounded-xl border border-transparent text-orange-500 bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-orange-500 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" href="../../../et/teenused/dokument.php">
                <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="m15 18-6-6 6-6" />
                </svg> Tagasi
              </a>
                        </div>
                        <!-- End Nupud -->
                    </div>

                </div>
            </div>

        </div>
    </main>
    <!-- ========== END MAIN CONTENT ========== -->

    <!-- ========== FOOTER ========== -->

    <!-- ========== END FOOTER ========== -->

    <!-- JS PLUGINS -->
    <!-- Required plugins -->
    <script src="../../../assets/vendor/preline/dist/index.js?v=3.1.0"></script>
    <!-- Clipboard -->
    <script src="../../../assets/vendor/clipboard/dist/clipboard.min.js"></script>
    <script src="../../../assets/js/hs-copy-clipboard-helper.js"></script>

    <script>
        window.addEventListener('load', () => {
            confetti({
                particleCount: 100,
                spread: 70,
                origin: {
                    y: 0.6
                }
            });
        });
    </script>

</body>

</html>