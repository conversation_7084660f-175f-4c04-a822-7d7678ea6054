
<?php
?>
<!DOCTYPE html>
<html lang="et" class="relative min-h-full light">

<head>
    <!-- LINK ‧ Meta -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="Märtsi 2025 elatise uudised: täitemenetluse algatamine, elatisabifond, laste ülalpidamiskohustus ja elatise maksmine pärast vanema kaotust. Ülevaade olulistest elatisega seotud teemadest.">
    <meta name="keywords" content="elatis, elatisraha, täitemenetlus, elatisabifond, laste ülalpidamine, kohtutäitur, elatise maksmine, elatise võlg">
    <meta name="author" content="Alimendid.ee">
    <meta property="og:title" content="Elatise uudised märtsis 2025" />
    <meta property="og:url" content="https://alimendid.ee/et/blogi/2025/elatise-uudised-martsis.php" />
    <meta property="og:image" content="https://alimendid.ee/assets/failid/pildid/blogi/2025/elatise-uudised-martsis.jpg" />
    <meta property="og:image:alt" content="Elatise uudised märtsis 2025" />
    <meta property="og:description" content="Märtsi 2025 elatise uudised: täitemenetluse algatamine, elatisabifond, laste ülalpidamiskohustus ja elatise maksmine pärast vanema kaotust. Ülevaade olulistest elatisega seotud teemadest." />
    <meta property="og:type" content="website" />
    <meta property="og:locale" content="et_ee" />
    <meta property='og:site_name' content='Alimendid.ee' />
    <meta property='article:author' content='https://www.facebook.com/Alimendid.ee' />
    <meta property='article:publisher' content='https://www.facebook.com/Alimendid.ee' />
    <meta property='article:published_time' content='2025-03-30' />

    <!-- Title -->
    <title>Elatise uudised märtsis 2025 ‧ Alimendid.ee</title>

    <!-- Favicon -->
    <link rel="shortcut icon" href="../../../assets/failid/favicon/favicon.ico">

    <!-- Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- CSS -->
    <link rel="stylesheet" href="../../../assets/css/main.min.css">
    <link href="../../../output.css" rel="stylesheet" />

    <!-- Theme Check and Update -->
    <script>
        const html = document.querySelector('html');
        const isLightOrAuto = localStorage.getItem('hs_theme') === 'light' || (localStorage.getItem('hs_theme') === 'auto' && !window.matchMedia('(prefers-color-scheme: dark)').matches);
        const isDarkOrAuto = localStorage.getItem('hs_theme') === 'dark' || (localStorage.getItem('hs_theme') === 'auto' && window.matchMedia('(prefers-color-scheme: dark)').matches);

        if (isLightOrAuto && html.classList.contains('dark')) html.classList.remove('dark');
        else if (isDarkOrAuto && html.classList.contains('light')) html.classList.remove('light');
        else if (isDarkOrAuto && !html.classList.contains('dark')) html.classList.add('dark');
        else if (isLightOrAuto && !html.classList.contains('light')) html.classList.add('light');
    </script>

    <!-- Google Ads-->
    <script>
        window.dataLayer = window.dataLayer || [];

        function gtag() {
            dataLayer.push(arguments);
        }
        gtag('js', new Date());

        gtag('config', 'UA-221277240-1');
    </script>

    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-C8JJCED3EQ"></script>
    <script>
        window.dataLayer = window.dataLayer || [];

        function gtag() {
            dataLayer.push(arguments);
        }
        gtag('js', new Date());

        gtag('config', 'G-C8JJCED3EQ');
    </script>

    <!-- Google Tag Manager -->
    <script>
        (function(w, d, s, l, i) {
            w[l] = w[l] || [];
            w[l].push({
                'gtm.start': new Date().getTime(),
                event: 'gtm.js'
            });
            var f = d.getElementsByTagName(s)[0],
                j = d.createElement(s),
                dl = l != 'dataLayer' ? '&l=' + l : '';
            j.async = true;
            j.src =
                'https://www.googletagmanager.com/gtm.js?id=' + i + dl;
            f.parentNode.insertBefore(j, f);
        })(window, document, 'script', 'dataLayer', 'GTM-T554HTP');
    </script>

    <!-- Hotjar -->
    <script>
        (function(h, o, t, j, a, r) {
            h.hj = h.hj || function() {
                (h.hj.q = h.hj.q || []).push(arguments)
            };
            h._hjSettings = {
                hjid: 3283746,
                hjsv: 6
            };
            a = o.getElementsByTagName('head')[0];
            r = o.createElement('script');
            r.async = 1;
            r.src = t + h._hjSettings.hjid + j + h._hjSettings.hjsv;
            a.appendChild(r);
        })(window, document, 'https://static.hotjar.com/c/hotjar-', '.js?sv=');
    </script>
</head>

<body class="dark:bg-neutral-900">
    <!-- Google Tag Manager -->
    <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-T554HTP" height="0" width="0"
            style="display:none;visibility:hidden"></iframe></noscript>

    <!-- ========== HEADER ========== -->
    <?php include '../../../assets/failid/komponendid/et/menu.php'; ?>
    <!-- ========== END HEADER ========== -->

    <!-- ========== MAIN CONTENT ========== -->
    <main id="content">

        <div class="max-w-3xl px-4 pt-6 lg:pt-10 pb-12 sm:px-6 lg:px-8 mx-auto">
            <img class="w-full object-cover rounded-xl mb-8" src="../../../assets/failid/pildid/blogi/2016/inpost/elatise-uudised-martsis.jpg" alt="Elatise uudised märtsis 2025">
            </h2>

            <!-- //LINK ‧ Pealkiri -->
            <h1 class="text-4xl lg:text-5xl md:text-5xl sm:text-4xl font-semibold mb-6 postitus-pealkiri">Elatise uudised märtsis 2025</h1>

            <div class="flex items-center gap-4 mb-6">
                <img class="size-12 rounded-full" src="../../../assets/failid/pildid/autor/alimendid.jpg" alt="Alimendid.ee">
                <!-- //LINK ‧ Kuupäev -->
                <div>
                    <div class="font-semibold text-gray-800 postitus-autor">Alimendid.ee</div>
                    <span class="text-sm text-gray-500 postitus-kuupaev">30.03.2025</span>
                    <span class="text-sm text-gray-500 postitus-middledot"> · </span>
                    <span class="text-sm text-gray-500 postitus-lugemine">2 minuti lugemine</span>
                </div>
            </div>

            <!-- //LINK ‧ Uudised -->
            <!-- Uudise algus -->
            <div class="news-item pb-4">
                <h3 class="text-xl font-semibold text-gray-800 uudis-pealkiri">
                    <a href="https://www.juristaitab.ee/et/elatis-taiskasvanud-oppivale-lapsele" target="_blank" style="transition: color 0.3s ease; color: inherit;" onmouseover="this.style.color='#ff6c00'" onmouseout="this.style.color='inherit'">
                        Elatis täiskasvanud õppivale lapsele
                    </a>
                </h3>
                <div class="mb-2 text-sm text-gray-500 pt-2">
                    <span class="text-md uudis-kuupaev">04.03.2025</span>
                    <span class="text-md uudis-middledot"> · </span>
                    <span class="text-md uudis-allikas">juristaitab.ee</span>
                </div>
                <p class="text-lg text-gray-700 pb-3 uudis-tekst">Elatis täisealisele lapsele erineb mõningal määral elatisest alaealisele lapsele. Kui alaealise lapse puhul eeldatakse, et tal puuduvad võimalused ise end üleval pidada, siis täisealise lapse puhul tuleb arvestada ka tema enda võimalusi elatusvahendeid teenida.<br><br>
                    Elatist saab nõuda kõigi eluvajaduste katmiseks (peaksid olema tõendatavad) kui täisealine laps õpingute tõttu ei saa ise endale ülalpidamist teenida. Vanemad vastutavad lapse toimetuleku eest üldjuhul võrdselt, seega saab ühelt vanemalt nõuda poole lapse vajaduste katmist.<br><br>
                    Seadus sätestab ülemiseks vanusepiiriks 21 eluaastat, mil on õppimise tõttu võimalik elatist nõuda. Kui õpingud lõppevad varem, lõppeb ka kohustus varem.<br><br>
                    Täiealise lapse puhul seadusjärgset miinimummäära ei ole. See kehtib alaealise lapse puhul. Täisealise ülalpidamisvajaduse kindlaks määramisel lähtutakse tema vajadustest ning võetakse arvesse kohustatud isiku võimalusi. Kui vanema ülalpidamisel on teisi isikuid, võetakse ka seda arvesse, kui vanem peaks sellele menetluses tuginema. Seejuures eelistatakse alaealisi lapse täisealistele.</p>
            </div>
            <!-- Uudise lõpp -->

            <!-- Uudise algus -->
            <div class="news-item pb-4">
                <h3 class="text-xl font-semibold text-gray-800 uudis-pealkiri">
                    <a href="https://www.juristaitab.ee/et/elatise-noudmine-kohtus" target="_blank" style="transition: color 0.3s ease; color: inherit;" onmouseover="this.style.color='#ff6c00'" onmouseout="this.style.color='inherit'">
                        Elatise nõudmine kohtus
                    </a>
                </h3>
                <div class="mb-2 text-sm text-gray-500 pt-2">
                    <span class="text-md uudis-kuupaev">10.03.2025</span>
                    <span class="text-md uudis-middledot"> · </span>
                    <span class="text-md uudis-allikas">juristaitab.ee</span>
                </div>
                <p class="text-lg text-gray-700 pb-3 uudis-tekst">Elatist on võimalik nõuda nii maksekäsukiirmenetluses kui hagimenetluses, kusjuures hagiavalduse vorm on leitav <a href="https://www.kohus.ee/dokumendid-ja-vormid/hagiavaldus-lapsele-elatise-valjamoistmiseks" target="_blank" style="color: #ff6c00;">kohtute veebilehel</a>. Rohkem elatise nõudmisest kohtus saate lugeda <a href="https://www.juristaitab.ee/et/kuidas-elatist-taotleda-eestis" target="_blank" style="color: #ff6c00;">siit</a>.</p>
            </div>
            <!-- Uudise lõpp -->

            <!-- Uudise algus -->
            <div class="news-item pb-4">
                <h3 class="text-xl font-semibold text-gray-800 uudis-pealkiri">
                    <a href="https://www.oiguskantsler.ee/sites/default/files/2025-03/Laenu%20votmine%20fuusilise%20isiku%20pankrotimenetluses.pdf" target="_blank" style="transition: color 0.3s ease; color: inherit;" onmouseover="this.style.color='#ff6c00'" onmouseout="this.style.color='inherit'">
                        Laenu võtmine füüsilise isiku pankrotimenetluses
                    </a>
                </h3>
                <div class="mb-2 text-sm text-gray-500 pt-2">
                    <span class="text-md uudis-kuupaev">04.03.2025</span>
                    <span class="text-md uudis-middledot"> · </span>
                    <span class="text-md uudis-allikas">oiguskantsler.ee</span>
                </div>
                <p class="text-lg text-gray-700 pb-3 uudis-tekst">Kohustustest vabastamise menetluse eesmärk on vabastada füüsilisest isikust võlgnik
                    pankrotimenetluses täitmata jäänud kohustustest, välja arvatud lapsele või vanemale elatise maksmise kohustusest
                    (<a href="https://www.riigiteataja.ee/akt/114032025014#para50lg2" target="_blank" style="color: #ff6c00;">FIMS § 44, § 50 lg 2</a>)
                    .</p>
            </div>
            <!-- Uudise lõpp -->

            <!-- Uudise algus -->
            <div class="news-item pb-4">
                <h3 class="text-xl font-semibold text-gray-800 uudis-pealkiri">
                    <a href="https://www.juristaitab.ee/et/taxonomy/term/21" target="_blank" style="transition: color 0.3s ease; color: inherit;" onmouseover="this.style.color='#ff6c00'" onmouseout="this.style.color='inherit'">
                        Suhete korraldamine abielu lõppedes
                    </a>
                </h3>
                <div class="mb-2 text-sm text-gray-500 pt-2">
                    <span class="text-md uudis-kuupaev">04.03.2025</span>
                    <span class="text-md uudis-middledot"> · </span>
                    <span class="text-md uudis-allikas">juristaitab.ee</span>
                </div>
                <p class="text-lg text-gray-700 pb-3 uudis-tekst">Lapse sünnist ja tema põlvnemise kindlaks tegemisest alates on vanematel ühine hooldusõigus, kui ei ole sündi registreerides kokku lepitud teisiti. Hooldusõiguse kuuluvust ei muuda vanemate lahutus ega lahuselu. Kui ühise hooldusõiguse teostamisel probleeme ei esine, ei ole ka põhjust hooldusõigusesse muudatusi teha. Lastega suhtlemise korra ja laste ülalpidamiskulude katmise jaotuse võivad vanemad kokku leppida omavahel suuliselt või lihtkirjalikult ning selle järgi toimida. <br><br>
                    Kui kokkulepet saavutada ei õnnestu, tuleks esmalt pöörduda <a href="https://sotsiaalkindlustusamet.ee/perelepitus" target="_blank" style="color: #ff6c00;">Sotsiaalkindlustusameti perelepitusteenusse</a> ja kui ka siis vanemaluskokkulepet ei sünni, laste elukohajärgsesse maakohtusse.</p>
            </div>
            <!-- Uudise lõpp -->

            <!-- Uudise algus -->
            <div class="news-item pb-4">
                <h3 class="text-xl font-semibold text-gray-800 uudis-pealkiri">
                    <a href="https://ekspress.delfi.ee/artikkel/120365447/huvasti-teslad-golf-ja-tenerife-paike-kahe-lapse-isa-saadeti-vangi-sest-ta-ei-maksa-elatist" target="_blank" style="transition: color 0.3s ease; color: inherit;" onmouseover="this.style.color='#ff6c00'" onmouseout="this.style.color='inherit'">
                        Kahe lapse isa saadeti vangi, sest ta ei maksa elatist
                    </a>
                </h3>
                <div class="mb-2 text-sm text-gray-500 pt-2">
                    <span class="text-md uudis-kuupaev">04.03.2025</span>
                    <span class="text-md uudis-middledot"> · </span>
                    <span class="text-md uudis-allikas">ekspress.delfi.ee</span>
                </div>
                <p class="text-lg text-gray-700 pb-3 uudis-tekst">Eestis on praegu 15 000 algatatud täiteasja elatisega seotud nõuete sissenõudmiseks. Kõik ei ole pahatahtlikud viilijad, paljudel ongi tööd ja raha vähe. <br><br>

                    Kui elatise maksmiseks kohustatud vanem ei täida oma kohustust või täidab seda osaliselt, võtab riik piiratud ulatuses elatise maksmise kohustuse üle.<br><br>

                    Kõige levinum on täitemenetluseaegne elatisabi. Selle saamise eelduseks on, et võlgniku suhtes on elatise sissenõudmiseks algatatud täitemenetlus. Täitemenetlusaegse elatisabi suurus ühele lapsele on kuni 200 eurot kalendrikuus.<br><br>

                    Kui võlgniku makstud elatise summa on väiksem kui 200 eurot kalendrikuus, on elatisabi suurus 200 euro ja makstud elatise vahe. Täitemenetlusaegset elatisabi ei maksta, kui võlgniku makstud summa on võrdne 200 euroga või suurem.</p>
            </div>
            <!-- Uudise lõpp -->

            <!-- Uudise algus -->
            <div class="news-item pb-4">
                <h3 class="text-xl font-semibold text-gray-800 uudis-pealkiri">
                    <a href="https://perejakodu.delfi.ee/artikkel/120256193/jurist-vastab-kuidas-saada-katte-kohtu-poolt-valja-moistetud-elatis-kui-vanem-seda-maksmast-keeldub" target="_blank" style="transition: color 0.3s ease; color: inherit;" onmouseover="this.style.color='#ff6c00'" onmouseout="this.style.color='inherit'">
                        Kuidas saada kätte kohtu poolt välja mõistetud elatis, kui vanem seda maksmast keeldub?
                    </a>
                </h3>
                <div class="mb-2 text-sm text-gray-500 pt-2">
                    <span class="text-md uudis-kuupaev">04.03.2025</span>
                    <span class="text-md uudis-middledot"> · </span>
                    <span class="text-md uudis-allikas">perejakodu.delfi.ee</span>
                </div>
                <p class="text-lg text-gray-700 pb-3 uudis-tekst">Täitemenetluse algatamiseks tuleb pöörduda avaldusega kohtutäituri poole. Kohtutäiturid on jaotatud tööpiirkondade järgi, seega tuleks sobiv täitur valida võlgniku elukoha järgi. Kui täitmisele esitatakse elatusraha nõue, kannab menetlusega seotud kulud võlgnik.<br><br>

                    Mõnikord võib juhtuda, et elatist ei ole võimalik kätte saada ka täitemenetluses ja elatist ei maksta üldse. Selliseks olukorraks, toetamaks üksinda last ülal pidavat vanemat, on riik loonud elatisabifondi. Täitemenetlusaegset elatisabi makstakse vanemale täitedokumendi alusel täitemenetluse ajal. Täitemenetlusaegse elatisabi saamise eelduseks on see, et täitemenetlus on kestnud ühe kalendrikuu. Kui pärast ühe kuu pikkust ooteperioodi ei ole elatist maksma kohustatud vanem lapsele elatist maksma hakanud, hakkab riik pidama arvestust selle üle, kas ja millises summas lapsele igakuiselt elatist laekub. Kui ooteperioodile järgneval kuul lapsele elatist ei laeku, hakkab riik lapsele maksma elatisabi kuni 200 euro ulatuses. See tähendab siis seda, et kui kohtuotsusega lapsele välja mõistetud elatis on väiksem kui 200 eurot kuus, makstakse ka elatisabi vastavalt täite-dokumendis välja mõistetud summale. Kui välja mõistetud elatis on aga suurem kui 200 eurot kuus, maksab riik elatist 200 euro ulatuses.
                </p>
            </div>
            <!-- Uudise lõpp -->

            <!-- Uudise algus -->
            <div class="news-item pb-4">
                <h3 class="text-xl font-semibold text-gray-800 uudis-pealkiri">
                    <a href="https://www.oiguskantsler.ee/sites/default/files/2025-03/Laste%20elukorraldus%20ja%20elatise%20maksmine%20lastele%20parast%20vanema%20kaotust.pdf" target="_blank" style="transition: color 0.3s ease; color: inherit;" onmouseover="this.style.color='#ff6c00'" onmouseout="this.style.color='inherit'">
                        Laste elukorraldus ja elatise maksmine lastele pärast vanema kaotust
                    </a>
                </h3>
                <div class="mb-2 text-sm text-gray-500 pt-2">
                    <span class="text-md uudis-kuupaev">04.03.2025</span>
                    <span class="text-md uudis-middledot"> · </span>
                    <span class="text-md uudis-allikas">oiguskantsler.ee</span>
                </div>
                <p class="text-lg text-gray-700 pb-3 uudis-tekst">Lapsevanem on kohustatud ülal pidama oma alaealist last ja last, kes täisealiseks saanuna
                    jätkab põhi- või keskhariduse omandamist põhikoolis, gümnaasiumis või kutseõppeasutuses kuni
                    lapse 21-aastaseks saamiseni. Lapse ülalpidamise kohustus on kehtestatud
                    lapse huvides ja seda näeb ette põhiseadus. Alaealine laps ei ole ise võimeline endale
                    toimetulekuks vajalikke vahendeid kindlustama, mistõttu peavad teda ülal pidama tema
                    vanemad. Vanem ei vabane alaealise lapse ülalpidamise kohustusest ka siis, kui ta ei ela lapsega
                    koos. Sellisel juhul peab vanem lapse
                    ülalpidamiseks maksma talle elatist. Kui vanem elatist ei maksa või ei tee seda
                    vajalikus ulatuses, lahendab vaidluse kohus. </p>
            </div>
            <!-- Uudise lõpp -->

            <!-- Uudise algus -->
            <div class="news-item pb-4">
                <h3 class="text-xl font-semibold text-gray-800 uudis-pealkiri">
                    <a href="https://perejakodu.delfi.ee/artikkel/120366591/elin-jarvsaar-elatis-pole-lahutuse-trahv-paljud-isad-kull-maksavad-ent-ei-adu-miks" target="_blank" style="transition: color 0.3s ease; color: inherit;" onmouseover="this.style.color='#ff6c00'" onmouseout="this.style.color='inherit'">
                        Elatis pole lahutuse trahv! Paljud isad küll maksavad, ent ei adu, miks
                    </a>
                </h3>
                <div class="mb-2 text-sm text-gray-500 pt-2">
                    <span class="text-md uudis-kuupaev">04.03.2025</span>
                    <span class="text-md uudis-middledot"> · </span>
                    <span class="text-md uudis-allikas">perejakodu.delfi.ee</span>
                </div>
                <p class="text-lg text-gray-700 pb-3 uudis-tekst">Kuni enamik peresid ei adu, et laps on kahe vanema ühine vastutus, ei muutu ka elatiste maksmisel midagi. Ja kui me päriselt suudame võrdse vanemluseni jõuda, ei ole ehk elatist vajagi – kasvatades ja panustades ka pärast lahutust ühiselt, pole ülekandeid endisele kaaslasele tingimata tarvis.<br><br>

                    Seni aga – kuni me riiklikult pole suutnud oma inimesi õpetada ja päriseluks ette valmistada, on vastutus vaja võtta justnimelt riigil. Kui kaua me lubame kesta sellel, et 120 miljonit jääb lastel saamata, söömata, kandmata? Riik peab siin tegema selgeid otsuseid – nõudma lastele elatise välja karmilt ja tõsiselt. Isegi, kui see tähendab elatisvõlglaste vangi mõistmist.</p>
            </div>
            <!-- Uudise lõpp -->

            <!-- Uudise algus -->
            <div class="news-item pb-4">
                <h3 class="text-xl font-semibold text-gray-800 uudis-pealkiri">
                    <a href="https://www.riigiteataja.ee/kohtulahendid/fail.html?fid=407624868" target="_blank" style="transition: color 0.3s ease; color: inherit;" onmouseover="this.style.color='#ff6c00'" onmouseout="this.style.color='inherit'">
                        Riigikohtu otsus nr 2-17-6713/196
                    </a>
                </h3>
                <div class="mb-2 text-sm text-gray-500 pt-2">
                    <span class="text-md uudis-kuupaev">04.03.2025</span>
                    <span class="text-md uudis-middledot"> · </span>
                    <span class="text-md uudis-allikas">riigiteataja.ee</span>
                </div>
                <p class="text-lg text-gray-700 pb-3 uudis-tekst"> Kui lapsevanem on maksnud lapsele elatist rohkem, kui ta oli selleks seaduse
                    või kohtulahendi järgi kohustatud, ei saa rohkem tasutud summat käsitada elatise ettemaksena ja
                    seeläbi vabastada vanemat tulevasest igakuise elatise tasumise kohustusest</p>
            </div>
            <!-- Uudise lõpp -->

        </div>

        <!-- Banner -->
        <?php include '../../../assets/failid/komponendid/et/blogi.php'; ?>
    </main>
    <!-- ========== END MAIN CONTENT ========== -->

    <!-- Footer -->
    <?php include '../../../assets/failid/komponendid/et/footer.php'; ?>

    <!-- JS PLUGINS -->
    <!-- Required plugins -->
    <script src="../../../assets/vendor/lodash/lodash.min.js"></script>
    <script src="../../../assets/vendor/preline/dist/index.js?v=3.0.1"></script>
    <!-- Clipboard -->
    <script src="../../../assets/vendor/clipboard/dist/clipboard.min.js"></script>
    <script src="../../../assets/js/hs-copy-clipboard-helper.js"></script>

    <script>
        window.addEventListener('load', () => {
            (function() {
                const tabsId = 'hs-pro-hero-tabs';
                const tabs = HSTabs.getInstance(`#${tabsId}`, true);
                const scrollNav = HSScrollNav.getInstance('#hs-pro-hero-tabs-scroll', true);

                tabs.element.on('change', ({
                    el
                }) => {
                    scrollNav.element.centerElement(el);
                });

                window.addEventListener('resize', _.debounce(() => {
                    scrollNav.element.centerElement(tabs.element.current);
                }, 100));

                window.addEventListener('change.hs.tab', ({
                    detail
                }) => {
                    if (detail.payload.tabsId !== tabsId) return false;

                    const tabs = document.querySelector('#hs-pro-hero-tabs-scroll');

                    window.scrollTo({
                        top: tabs.offsetTop,
                        behavior: 'smooth'
                    });
                });
            })();
        });
    </script>

</body>

</html>