
<?php
?>
<!DOCTYPE html>
<html lang="et" class="relative min-h-full light">

<head>
    <!-- LINK ‧ Meta -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="Aprilli 2025 elatise uudised: elatisabi tõus, enammakstud elatise tasaarvestamine, perehüvitiste maksmine akadeemilise puhkuse ajal. Ülevaade olulistest elatisega seotud teemadest.">
    <meta name="keywords" content="elatis, elatisraha, elatisabi, elatise maksmine, perehüvitised, akadeemiline puhkus, elatise tasaarvestamine, pere hülgamine">
    <meta name="author" content="Alimendid.ee">
    <meta property="og:title" content="Elatise uudised aprillis 2025" />
    <meta property="og:url" content="https://alimendid.ee/et/blogi/2025/elatise-uudised-aprillis.php" />
    <meta property="og:image" content="https://alimendid.ee/assets/failid/pildid/blogi/2025/elatise-uudised-aprillis.jpg" />
    <meta property="og:image:alt" content="Elatise uudised aprillis 2025" />
    <meta property="og:description" content="Aprilli 2025 elatise uudised: elatisabi tõus, enammakstud elatise tasaarvestamine, perehüvitiste maksmine akadeemilise puhkuse ajal. Ülevaade olulistest elatisega seotud teemadest." />
    <meta property="og:type" content="website" />
    <meta property="og:locale" content="et_ee" />
    <meta property='og:site_name' content='Alimendid.ee' />
    <meta property='article:author' content='https://www.facebook.com/Alimendid.ee' />
    <meta property='article:publisher' content='https://www.facebook.com/Alimendid.ee' />
    <meta property='article:published_time' content='2025-04-30' />

    <!-- Title -->
    <title>Elatise uudised aprillis 2025 ‧ Alimendid.ee</title>

    <!-- Favicon -->
    <link rel="shortcut icon" href="../../../assets/failid/favicon/favicon.ico">

    <!-- Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- CSS -->
    <link rel="stylesheet" href="../../../assets/css/main.min.css">
    <link href="../../../output.css" rel="stylesheet" />

    <!-- Theme Check and Update -->
    <script>
        const html = document.querySelector('html');
        const isLightOrAuto = localStorage.getItem('hs_theme') === 'light' || (localStorage.getItem('hs_theme') === 'auto' && !window.matchMedia('(prefers-color-scheme: dark)').matches);
        const isDarkOrAuto = localStorage.getItem('hs_theme') === 'dark' || (localStorage.getItem('hs_theme') === 'auto' && window.matchMedia('(prefers-color-scheme: dark)').matches);

        if (isLightOrAuto && html.classList.contains('dark')) html.classList.remove('dark');
        else if (isDarkOrAuto && html.classList.contains('light')) html.classList.remove('light');
        else if (isDarkOrAuto && !html.classList.contains('dark')) html.classList.add('dark');
        else if (isLightOrAuto && !html.classList.contains('light')) html.classList.add('light');
    </script>

    <!-- Google Ads-->
    <script>
        window.dataLayer = window.dataLayer || [];

        function gtag() {
            dataLayer.push(arguments);
        }
        gtag('js', new Date());

        gtag('config', 'UA-221277240-1');
    </script>

    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-C8JJCED3EQ"></script>
    <script>
        window.dataLayer = window.dataLayer || [];

        function gtag() {
            dataLayer.push(arguments);
        }
        gtag('js', new Date());

        gtag('config', 'G-C8JJCED3EQ');
    </script>

    <!-- Google Tag Manager -->
    <script>
        (function(w, d, s, l, i) {
            w[l] = w[l] || [];
            w[l].push({
                'gtm.start': new Date().getTime(),
                event: 'gtm.js'
            });
            var f = d.getElementsByTagName(s)[0],
                j = d.createElement(s),
                dl = l != 'dataLayer' ? '&l=' + l : '';
            j.async = true;
            j.src =
                'https://www.googletagmanager.com/gtm.js?id=' + i + dl;
            f.parentNode.insertBefore(j, f);
        })(window, document, 'script', 'dataLayer', 'GTM-T554HTP');
    </script>

    <!-- Hotjar -->
    <script>
        (function(h, o, t, j, a, r) {
            h.hj = h.hj || function() {
                (h.hj.q = h.hj.q || []).push(arguments)
            };
            h._hjSettings = {
                hjid: 3283746,
                hjsv: 6
            };
            a = o.getElementsByTagName('head')[0];
            r = o.createElement('script');
            r.async = 1;
            r.src = t + h._hjSettings.hjid + j + h._hjSettings.hjsv;
            a.appendChild(r);
        })(window, document, 'https://static.hotjar.com/c/hotjar-', '.js?sv=');
    </script>
</head>

<body class="dark:bg-neutral-900">
    <!-- Google Tag Manager -->
    <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-T554HTP" height="0" width="0"
            style="display:none;visibility:hidden"></iframe></noscript>

    <!-- ========== HEADER ========== -->
    <?php include '../../../assets/failid/komponendid/et/menu.php'; ?>
    <!-- ========== END HEADER ========== -->

    <!-- ========== MAIN CONTENT ========== -->
    <main id="content">

        <div class="max-w-3xl px-4 pt-6 lg:pt-10 pb-12 sm:px-6 lg:px-8 mx-auto">
            <img class="w-full object-cover rounded-xl mb-8" src="../../../assets/failid/pildid/blogi/2016/inpost/elatise-uudised-aprillis.jpg" alt="Elatise uudised aprillis 2025">
            </h2>

            <!-- //LINK ‧ Pealkiri -->
            <h1 class="text-4xl lg:text-5xl md:text-5xl sm:text-4xl font-semibold mb-6 postitus-pealkiri">Elatise uudised aprillis 2025</h1>

            <div class="flex items-center gap-4 mb-6">
                <img class="size-12 rounded-full" src="../../../assets/failid/pildid/autor/alimendid.jpg" alt="Alimendid.ee">
                <!-- //LINK ‧ Kuupäev -->
                <div>
                    <div class="font-semibold text-gray-800 postitus-autor">Alimendid.ee</div>
                    <span class="text-sm text-gray-500 postitus-kuupaev">30.04.2025</span>
                    <span class="text-sm text-gray-500 postitus-middledot"> · </span>
                    <span class="text-sm text-gray-500 postitus-lugemine">2 minuti lugemine</span>
                </div>
            </div>

            <!-- //LINK ‧ Uudised -->
            <!-- Uudise algus -->
            <div class="news-item pb-4">
                <h3 class="text-xl font-semibold text-gray-800 uudis-pealkiri">
                    <a href="https://www.juristaitab.ee/et/taxonomy/term/28?page=1" target="_blank" style="transition: color 0.3s ease; color: inherit;" onmouseover="this.style.color='#ff6c00'" onmouseout="this.style.color='inherit'">
                        Ülalpidamiskohustuse alus
                    </a>
                </h3>
                <div class="mb-2 text-sm text-gray-500 pt-2">
                    <span class="text-md uudis-kuupaev">07.04.2025</span>
                    <span class="text-md uudis-middledot"> · </span>
                    <span class="text-md uudis-allikas">juristaitab.ee</span>
                </div>
                <p class="text-lg text-gray-700 pb-3 uudis-tekst">Ülalpidamiskohustuse juures ei ole oluline kas last sooviti ja millised olid lapse eostamise asjaolud. Oluline on, et laps on sündinud, põlvneb oma vanematest, vajab ülalpidamiseks rahalisi vahendeid ja neid vahendeid peavad andma tema vanemad.</p>
            </div>
            <!-- Uudise lõpp -->

            <!-- Uudise algus -->
            <div class="news-item pb-4">
                <h3 class="text-xl font-semibold text-gray-800 uudis-pealkiri">
                    <a href="https://www.juristaitab.ee/et/taxonomy/term/33" target="_blank" style="transition: color 0.3s ease; color: inherit;" onmouseover="this.style.color='#ff6c00'" onmouseout="this.style.color='inherit'">
                        Täisealise isiku abivajadus
                    </a>
                </h3>
                <div class="mb-2 text-sm text-gray-500 pt-2">
                    <span class="text-md uudis-kuupaev">17.04.2025</span>
                    <span class="text-md uudis-middledot"> · </span>
                    <span class="text-md uudis-allikas">juristaitab.ee</span>
                </div>
                <p class="text-lg text-gray-700 pb-3 uudis-tekst">Alla 21-aastane õppiv laps on õigustatud ülalpidamist saama õpingute ajal. Muu täisealise isiku õigus saada ülalpidamist sõltub tema abivajadusest. Kui täisealine inimene ei ole võimeline ise ennast ülal pidama, see tähendab, et tema sissetulekust ja vara realiseerimisest ei piisa tema igapäevase toimetuleku tagamiseks ning tal puudub abikaasa või registreeritud elukaaslane, kes teda aidata suudaks, tekib tema esimese astme sugulastel (vanematel ja lastel) kohustus teda ülal pidada. Muudel sugulastel ülalpidamiskohustust ei ole.<br><br>
                    See, kui suures summas peaks ülalpidamist andma, sõltub täisealise õppiva lapse või muu abi vajava sugulase puhul nii tema vajadustest ja tavalisest elulaadist kui ka kohustatud isiku võimalustest.
                </p>
            </div>
            <!-- Uudise lõpp -->

            <!-- Uudise algus -->
            <div class="news-item pb-4">
                <h3 class="text-xl font-semibold text-gray-800 uudis-pealkiri">
                    <a href="https://www.sm.ee/blogi/age-viira-pere-hulgamisega-jaetakse-laps-tihti-vaesusesse" target="_blank" style="transition: color 0.3s ease; color: inherit;" onmouseover="this.style.color='#ff6c00'" onmouseout="this.style.color='inherit'">
                        Pere hülgamisega jäetakse laps tihti vaesusesse
                    </a>
                </h3>
                <div class="mb-2 text-sm text-gray-500 pt-2">
                    <span class="text-md uudis-kuupaev">22.04.2025</span>
                    <span class="text-md uudis-middledot"> · </span>
                    <span class="text-md uudis-allikas">sm.ee</span>
                </div>
                <p class="text-lg text-gray-700 pb-3 uudis-tekst">2024. aastal tõusis elatisabi 100-lt eurolt 200 euroni nendele üksikvanematele, kelle lapse teine vanem ei täida ülalpidamiskohustust ja ei toeta oma last. Võrreldes elatisvõlgnike arvuga saab elatisabi vaid 30% nendest, kes seda tegelikult võiksid saada.</p>
            </div>
            <!-- Uudise lõpp -->

            <!-- Uudise algus -->
            <div class="news-item pb-4">
                <h3 class="text-xl font-semibold text-gray-800 uudis-pealkiri">
                    <a href="http://www.vastused.ee/loe/oigus/perekonnaoigus/1.html" target="_blank" style="transition: color 0.3s ease; color: inherit;" onmouseover="this.style.color='#ff6c00'" onmouseout="this.style.color='inherit'">
                        Kas lapse isa võib maha arvata enammakstud elatise summa, mida ta kuni kohtuotsuseni rohkem maksis?
                    </a>
                </h3>
                <div class="mb-2 text-sm text-gray-500 pt-2">
                    <span class="text-md uudis-kuupaev">07.04.2025</span>
                    <span class="text-md uudis-middledot"> · </span>
                    <span class="text-md uudis-allikas">vastused.ee</span>
                </div>
                <p class="text-lg text-gray-700 pb-3 uudis-tekst">Üldjoontes peaks elatisvõlgnevuse tasumisel arvestama kõiki summasid, mis on elatisena tasutud. Tasaarvestamisele ei kuulu summad, mis on kunagi makstud ja mille arvelt soovitakse edasiulatuvalt vähem maksta, kuid juba tekkinud võla mõttes peaks minu hinnangul makstud summad siiski võlga nö kustutama.</p>
            </div>
            <!-- Uudise lõpp -->

            <!-- Uudise algus -->
            <div class="news-item pb-4">
                <h3 class="text-xl font-semibold text-gray-800 uudis-pealkiri">
                    <a href="https://www.sotsiaalkindlustusamet.ee/uudised/perehuvitiste-maksmine-peatub-akadeemilise-puhkuse-ajaks" target="_blank" style="transition: color 0.3s ease; color: inherit;" onmouseover="this.style.color='#ff6c00'" onmouseout="this.style.color='inherit'">
                        Perehüvitiste maksmine peatub akadeemilise puhkuse ajaks
                    </a>
                </h3>
                <div class="mb-2 text-sm text-gray-500 pt-2">
                    <span class="text-md uudis-kuupaev">30.04.2025</span>
                    <span class="text-md uudis-middledot"> · </span>
                    <span class="text-md uudis-allikas">sotsiaalkindlustusamet.ee</span>
                </div>
                <p class="text-lg text-gray-700 pb-3 uudis-tekst">Kui üle 19-aastane õppiv laps jääb akadeemilisele puhkusele, peatab sotsiaalkindlustusamet peretoetuste maksmise kuust, mis järgneb akadeemilisele puhkusele jäämisele. Hüvitiste maksmine jätkub pärast seda, kui laps jätkab õppimist. <br><br>

                    Peretoetuste ja elatisabi maksmise üks eesmärk on pärast lapse 19. eluaastat toetada tema aktiivseid õpinguid. Akadeemilisel puhkusel viibimine tähendab, et õppimine on peatatud, mistõttu ei ole toetuse maksmine sel ajal põhjendatud. Samasugust põhimõtet rakendatakse ka õppetoetuste ja stipendiumite puhul. <br><br>

                    Sotsiaalkindlustusamet saab lapse akadeemilise puhkuse ja selle lõppemise kohta vajalikud andmed otse Eesti hariduse infosüsteemist. Inimesel endal ei ole vaja ametit teavitada, välja arvatud juhul, kui laps õpib välisriigis. Hüvitiste maksmine peatub olenemata sellest, miks laps akadeemilisele puhkusele jäi. </p>
            </div>
            <!-- Uudise lõpp -->

        </div>

        <!-- Banner -->
        <?php include '../../../assets/failid/komponendid/et/blogi.php'; ?>
    </main>
    <!-- ========== END MAIN CONTENT ========== -->

    <!-- Footer -->
    <?php include '../../../assets/failid/komponendid/et/footer.php'; ?>

    <!-- JS PLUGINS -->
    <!-- Required plugins -->
    <script src="../../../assets/vendor/lodash/lodash.min.js"></script>
    <script src="../../../assets/vendor/preline/dist/index.js?v=3.0.1"></script>
    <!-- Clipboard -->
    <script src="../../../assets/vendor/clipboard/dist/clipboard.min.js"></script>
    <script src="../../../assets/js/hs-copy-clipboard-helper.js"></script>

    <script>
        window.addEventListener('load', () => {
            (function() {
                const tabsId = 'hs-pro-hero-tabs';
                const tabs = HSTabs.getInstance(`#${tabsId}`, true);
                const scrollNav = HSScrollNav.getInstance('#hs-pro-hero-tabs-scroll', true);

                tabs.element.on('change', ({
                    el
                }) => {
                    scrollNav.element.centerElement(el);
                });

                window.addEventListener('resize', _.debounce(() => {
                    scrollNav.element.centerElement(tabs.element.current);
                }, 100));

                window.addEventListener('change.hs.tab', ({
                    detail
                }) => {
                    if (detail.payload.tabsId !== tabsId) return false;

                    const tabs = document.querySelector('#hs-pro-hero-tabs-scroll');

                    window.scrollTo({
                        top: tabs.offsetTop,
                        behavior: 'smooth'
                    });
                });
            })();
        });
    </script>

</body>

</html>