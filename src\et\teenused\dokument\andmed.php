    <!-- Alimendid.ee andmebaas -->
    <?php
    require_once '../alimendid-teenused.php';
    require_once '../../../andmebaas/db_config.php';
    require_once '../../../andmebaas/dokument/dokument_db.php';

    // Käsitle vormi esitamist
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
      // Debug: logi POST andmed
      error_log("POST andmed: " . print_r($_POST, true));

      $result = save_dokument_data($_POST, $_FILES);

      if ($result['success']) {
        // Suuna kinnitus lehele
        header("Location: kinnitus.php?id=" . $result['elatise_id']);
        exit;
      } else {
        $error_message = $result['message'];
      }
    }

    // Võta paketi parameeter URL-ist
    $pakett = $_GET['pakett'] ?? 'dokument-odav';

    // Kontrolli, kas pakett on kehtiv
    $kehtivad_paketid = ['dokument-odav', 'dokument-keskmine', 'dokument-kallis'];
    if (!in_array($pakett, $kehtivad_paketid)) {
        $pakett = 'dokument-odav';
    }

    // Võta paketi andmed
    $paketi_andmed = $alimendid_teenus['dokument'][$pakett];
    $hind = $paketi_andmed['hind_kokku'];
    $tahtpaev = $paketi_andmed['tahtpaev'];
    ?>

<!DOCTYPE html>
<html lang="en" class="relative min-h-full">

<head>
  <!-- Required Meta Tags Always Come First -->
  <meta charset="utf-8">
  <meta name="robots" content="max-snippet:-1, max-image-preview:large, max-video-preview:-1">
  <link rel="canonical" href="https://preline.co/">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <meta name="description" content="Coffee Shop demo showcases a modern, elegant interface for browsing seasonal products, exploring artisanal beverages, and experiencing a unique blend of coffee culture and artistic ambiance.">

  <meta name="twitter:site" content="@preline">
  <meta name="twitter:creator" content="@preline">
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:title" content="Coffee Shop | Preline Pro | Preline UI, crafted with Tailwind CSS">
  <meta name="twitter:description" content="Coffee Shop demo showcases a modern, elegant interface for browsing seasonal products, exploring artisanal beverages, and experiencing a unique blend of coffee culture and artistic ambiance.">
  <meta name="twitter:image" content="https://preline.co/assets/img/og-image.png">

  <meta property="og:url" content="https://preline.co/">
  <meta property="og:locale" content="en_US">
  <meta property="og:type" content="website">
  <meta property="og:site_name" content="Preline">
  <meta property="og:title" content="Coffee Shop | Preline Pro | Preline UI, crafted with Tailwind CSS">
  <meta property="og:description" content="Coffee Shop demo showcases a modern, elegant interface for browsing seasonal products, exploring artisanal beverages, and experiencing a unique blend of coffee culture and artistic ambiance.">
  <meta property="og:image" content="https://preline.co/assets/img/og-image.png">

  <!-- Title -->
  <title>Coffee Shop | Preline Pro | Preline UI, crafted with Tailwind CSS</title>

  <!-- Favicon -->
  <link rel="shortcut icon" href="../favicon.ico">

  <!-- Font -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">


  <!-- CSS HS -->
  <link href="../../../output.css" rel="stylesheet" />
  <link rel="stylesheet" href="../../../assets/css/main.min.css?v=3.1.0">

  <!-- Theme Check and Update -->
  <script>
    const html = document.querySelector('html');
    const isLightOrAuto = localStorage.getItem('hs_theme') === 'light' || (localStorage.getItem('hs_theme') === 'auto' && !window.matchMedia('(prefers-color-scheme: dark)').matches);
    const isDarkOrAuto = localStorage.getItem('hs_theme') === 'dark' || (localStorage.getItem('hs_theme') === 'auto' && window.matchMedia('(prefers-color-scheme: dark)').matches);

    if (isLightOrAuto && html.classList.contains('dark')) html.classList.remove('dark');
    else if (isDarkOrAuto && html.classList.contains('light')) html.classList.remove('light');
    else if (isDarkOrAuto && !html.classList.contains('dark')) html.classList.add('dark');
    else if (isLightOrAuto && !html.classList.contains('light')) html.classList.add('light');
  </script>
</head>

<body class="dark:bg-neutral-900">
  <!-- ========== HEADER ========== -->
  <header class="">

    <!-- Google Tag Manager (noscript) -->
    <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-T554HTP" height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>

    <?php include '../../../assets/failid/komponendid/et/menu.php'; ?>
  </header>
  <!-- ========== END HEADER ========== -->

  <!-- ========== MAIN CONTENT ========== -->
  <main id="content">

    <!-- //LINK - Vormi laius -->
    <div class="max-w-xl px-4 sm:px-6 lg:px-8 py-12 lg:py-24 mx-auto">

      <!-- //LINK - Pealkiri -->
      <div class="mb-6 sm:mb-10 max-w-2xl text-center mx-auto">
        <h1 class="font-medium text-black text-3xl sm:text-4xl dark:text-white pb-2">
          Elatise dokument
        </h1>
        <p class="text-gray-600 dark:text-gray-300">
          Juristi poolt elatise dokumendi koostamine
        </p>
      </div>

      <!-- Form -->
      <form method="POST" action="kinnitus.php">
        <!-- Peidetud paketi väli -->
        <input type="hidden" name="pakett" value="<?php echo htmlspecialchars($pakett); ?>">

        <!-- //LINK - Laius -->
        <div class="lg:max-w-xl mx-auto lg:mx-auto">

          <!-- Card -->
          <div class="">

            <!-- //SECTION - KLIENT -->
            <div class="pb-8 mb-8 border-b border-gray-100 last:pb-0 last:mb-0 last:border-b-0 dark:border-neutral-700">
              <div class="space-y-4">
                <h2 class="mb-1 font-medium text-xl text-gray-800 dark:text-neutral-200">
                  Minu andmed
                </h2>
                <p class="pb-1 text-base text-gray-500 dark:text-neutral-400 max-w-md">
                  Sisesta enda andmed
                </p>
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  <div>
                    <!-- //LINK - Nimi -->
                    <input name="klient_nimi" id="id-klient-nimi" type="text" class="py-3 px-4 block w-full border-gray-200 rounded-xl placeholder:text-gray-400 ring-1 ring-transparent focus:border-orange-500 focus:ring-orange-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-transparent dark:border-neutral-700 dark:text-neutral-300 dark:placeholder:text-white/60 dark:focus:ring-neutral-600" placeholder="Nimi" aria-label="Nimi">
                  </div>
                  <div>
                    <!-- //LINK - Isikukood -->
                    <input name="klient_isikukood" id="id-klient-isikukood" type="text" pattern="[0-9]{11}" maxlength="11" class="py-3 px-4 block w-full border-gray-200 rounded-xl placeholder:text-gray-400 ring-1 ring-transparent focus:border-orange-500 focus:ring-orange-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-transparent dark:border-neutral-700 dark:text-neutral-300 dark:placeholder:text-white/60 dark:focus:ring-neutral-600" placeholder="Isikukood" aria-label="Isikukood">
                  </div>

                  <!-- //LINK - Aadress -->
                  <div>
                    <input name="klient_aadress" id="id-klient-aadress" type="text" class="py-3 px-4 block w-full border-gray-200 rounded-xl placeholder:text-gray-400 ring-2 ring-transparent focus:border-orange-500 focus:ring-orange-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-transparent dark:border-neutral-700 dark:text-neutral-300 dark:placeholder:text-white/60 dark:focus:ring-neutral-600" placeholder="Aadress" aria-label="Aadress">
                  </div>

                  <div>
                    <!-- //LINK - E-post -->
                    <input name="klient_post" id="id-klient-epost" type="email" class="py-3 px-4 block w-full border-gray-200 rounded-xl placeholder:text-gray-400 ring-1 ring-transparent focus:border-orange-500 focus:ring-orange-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-transparent dark:border-neutral-700 dark:text-neutral-300 dark:placeholder:text-white/60 dark:focus:ring-neutral-600" placeholder="E-post" aria-label="E-post">
                  </div>

                  <div>
                    <!-- //LINK - Telefon -->
                    <input name="klient_telefon" id="id-klient-telefon" type="text" class="py-3 px-4 block w-full border-gray-200 rounded-xl placeholder:text-gray-400 ring-1 ring-transparent focus:border-orange-500 focus:ring-orange-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-transparent dark:border-neutral-700 dark:text-neutral-300 dark:placeholder:text-white/60 dark:focus:ring-neutral-600" placeholder="Telefon" aria-label="Telefon">
                  </div>

                </div>

              </div>
              <!-- //!SECTION - KLIENT -->
            </div>

            <!-- //SECTION - MAKSJA -->
            <div class="pb-8 mb-8 border-b border-gray-100 last:pb-0 last:mb-0 last:border-b-0 dark:border-neutral-700">
              <div class="space-y-4">
                <h2 class="mb-1 font-medium text-xl text-gray-800 dark:text-neutral-200">
                  Teise vanema andmed
                </h2>
                <p class="pb-1 text-base text-gray-500 dark:text-neutral-400 max-w-md">
                  Sisesta teise vanema andmed
                </p>
                <!-- //LINK - Nimi -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  <div>
                    <input name="teine_vanem_nimi" id="id-maksja-nimi" type="text" class="py-3 px-4 block w-full border-gray-200 rounded-xl placeholder:text-gray-400 ring-1 ring-transparent focus:border-orange-500 focus:ring-orange-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-transparent dark:border-neutral-700 dark:text-neutral-300 dark:placeholder:text-white/60 dark:focus:ring-neutral-600" placeholder="Nimi" aria-label="Nimi">
                  </div>

                  <!-- //LINK - Isikukood -->
                  <div>
                    <input name="teine_vanem_isikukood" id="id-maksja-isikukood" type="text" pattern="[0-9]{11}" maxlength="11" class="py-3 px-4 block w-full border-gray-200 rounded-xl placeholder:text-gray-400 ring-1 ring-transparent focus:border-orange-500 focus:ring-orange-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-transparent dark:border-neutral-700 dark:text-neutral-300 dark:placeholder:text-white/60 dark:focus:ring-neutral-600" placeholder="Isikukood" aria-label="Isikukood">
                  </div>

                  <!-- //LINK - Aadress -->
                  <div>
                    <input name="teine_vanem_aadress" id="id-maksja-aadress" type="text" class="py-3 px-4 block w-full border-gray-200 rounded-xl placeholder:text-gray-400 ring-2 ring-transparent focus:border-orange-500 focus:ring-orange-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-transparent dark:border-neutral-700 dark:text-neutral-300 dark:placeholder:text-white/60 dark:focus:ring-neutral-600" placeholder="Aadress" aria-label="Aadress">
                  </div>

                  <!-- //LINK - E-post -->
                  <div>
                    <input name="teine_vanem_email" id="id-maksja-epost" type="email" class="py-3 px-4 block w-full border-gray-200 rounded-xl placeholder:text-gray-400 ring-1 ring-transparent focus:border-orange-500 focus:ring-orange-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-transparent dark:border-neutral-700 dark:text-neutral-300 dark:placeholder:text-white/60 dark:focus:ring-neutral-600" placeholder="E-post" aria-label="E-post">
                  </div>

                  <!-- //LINK - Telefon -->
                  <div>
                    <input name="teine_vanem_telefon" id="id-maksja-telefon" type="text" class="py-3 px-4 block w-full border-gray-200 rounded-xl placeholder:text-gray-400 ring-1 ring-transparent focus:border-orange-500 focus:ring-orange-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-transparent dark:border-neutral-700 dark:text-neutral-300 dark:placeholder:text-white/60 dark:focus:ring-neutral-600" placeholder="Telefon" aria-label="Telefon">
                  </div>
                </div>

              </div>
              <!-- //!SECTION - Maksja andmed -->
            </div>

            <!-- //SECTION - LAPSED -->
            <div class="pb-8 mb-8 border-b border-gray-100 last:pb-0 last:mb-0 last:border-b-0 dark:border-neutral-700">
              <div class="space-y-4">
                <h2 class="mb-1 font-medium text-xl text-gray-800 dark:text-neutral-200">
                  Lapse andmed
                </h2>
                <p class="pb-1 text-base text-gray-500 dark:text-neutral-400 max-w-md">
                  Sisesta lapse andmed
                </p>
                <!-- Laste sisestamise vorm -->
                <div id="lapsed-konteiner" class="space-y-6">

                  <!-- Esimene laps -->
                  <div id="laps-1" class="space-y-4">

                    <!-- Lapse nimi ja isikukood -->
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      <div>
                        <input type="text" name="lapse_nimi[]" class="py-3 px-4 block w-full border-gray-200 rounded-xl placeholder:text-gray-400 focus:border-orange-500 focus:ring-orange-500 disabled:opacity-50 disabled:pointer-events-none focus:ring-1" placeholder="Nimi">
                      </div>
                      <div>
                        <input type="text" name="lapse_isikukood[]" pattern="[0-9]{11}" maxlength="11" class="py-3 px-4 block w-full border-gray-200 rounded-xl placeholder:text-gray-400 focus:border-orange-500 focus:ring-orange-500 disabled:opacity-50 disabled:pointer-events-none focus:ring-1" placeholder="Isikukood">
                      </div>
                    </div>

                    <!-- Ööpäevade arv -->
                    <div>
                      <label class="block mb-2 text-md text-gray-800 dark:text-neutral-200">
                        Mitu ööpäeva viibib laps teise vanemaga?
                        <span class="hs-tooltip inline-block align-middle ms-1">
                          <svg class="shrink-0 size-3.5 text-stone-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <circle cx="12" cy="12" r="10" />
                            <path d="M12 16v-4" />
                            <path d="M12 8h.01" />
                          </svg>
                          <span class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible opacity-0 transition-opacity hidden invisible z-60 max-w-60 py-1 px-2 bg-stone-900 text-xs font-normal text-white rounded-lg shadow-2xs" role="tooltip">
                            Mitu ööpäeva kuus viibib laps keskmiselt teise vanemaga?
                          </span>
                        </span>
                      </label>

                      <!-- Select -->
                      <div class="relative">
                        <select name="lapse_paevade_arv[]" data-hs-select='{
        "placeholder": "Vali...",
        "hasSearch": false,
        "searchPlaceholder": "Vali",
        "searchClasses": "block py-1.5 sm:py-2 px-3 w-full sm:text-sm border-gray-200 rounded-lg focus:border-orange-500 focus:ring-orange-500 dark:bg-neutral-950 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder:text-neutral-400",
        "searchWrapperClasses": "bg-white p-2 -mx-1 sticky top-0 dark:bg-neutral-950",
        "toggleTag": "<button type=\"button\" aria-expanded=\"false\"><span class=\"flex\" data-icon></span><span class=\"text-gray-400 dark:text-neutral-200\" data-title></span></button>",
        "toggleClasses": "focus:ring-1 hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 relative py-3 ps-4 pe-9 flex items-center gap-x-2 text-nowrap w-full cursor-pointer bg-white border border-gray-200 rounded-xl text-start text-sm sm:text-base ring-1 ring-transparent hover:border-orange-500 hover:ring-orange-500 focus:outline-hidden focus:ring-orange-500 dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:focus:outline-hidden dark:hover:ring-neutral-600 dark:focus:ring-neutral-600",

        "dropdownClasses": "z-80 max-h-72 p-1 pt-0 space-y-0.5 z-50 w-full overflow-hidden overflow-y-auto bg-white rounded-xl shadow-xl [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500 dark:bg-neutral-950",
        "optionClasses": "hs-selected:bg-gray-100 dark:hs-selected:bg-neutral-800 py-2 px-4 w-full text-sm text-gray-800 cursor-pointer hover:bg-gray-100 rounded-lg focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800",
        "optionTemplate": "<div><div class=\"flex items-center\"><div class=\"shrink-0 me-2\" data-icon></div><div class=\"text-gray-800 dark:text-neutral-200\" data-title></div></div></div>"
      }' class="hidden">
                          <option value="">Vali</option>
                          <option value="1">1 ööpäeva</option>
                          <option value="2">2 ööpäeva</option>
                          <option value="3">3 ööpäeva</option>
                          <option value="4">4 ööpäeva</option>
                          <option value="5">5 ööpäeva</option>
                          <option value="6">6 ööpäeva</option>
                          <option value="7">7 ööpäeva</option>
                          <option value="8">8 ööpäeva</option>
                          <option value="9">9 ööpäeva</option>
                          <option value="10">10 ööpäeva</option>
                          <option value="11">11 ööpäeva</option>
                          <option value="12">12 ööpäeva</option>
                          <option value="13">13 ööpäeva</option>
                          <option value="14">14 ööpäeva</option>
                          <option value="15">15 ööpäeva</option>
                        </select>

                        <div class="absolute top-1/2 end-2.5 -translate-y-1/2">
                          <svg class="shrink-0 size-3.5 text-gray-500 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="m7 15 5 5 5-5" />
                            <path d="m7 9 5-5 5 5" />
                          </svg>
                        </div>
                      </div>
                      <!-- End Select -->
                    </div>
                  </div>
                </div>

                <!-- Lisa laps nupp -->
                <div class="mt-2 flex gap-2 justify-end">
                  <button type="button" id="kustuta-laps-nupp" class="py-1.5 px-2 inline-flex items-center gap-x-1 text-sm text-orange-500 font-normal rounded-xl border border-dashed border-gray-200 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-50 hidden">
                    <svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M5 12h14" />
                    </svg>
                    Kustuta laps
                  </button>
                  <button type="button" id="lisa-laps-nupp" class="py-1.5 px-2 inline-flex items-center gap-x-1 text-sm text-orange-500 font-normal rounded-xl border border-dashed border-gray-200 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-50">
                    <svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M5 12h14" />
                      <path d="M12 5v14" />
                    </svg>
                    Lisa laps
                  </button>
                </div>

                <!-- //LINK - Lapsetoetus -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">

                  <!-- //LINK - Makstud elatis -->
                  <div>
                    <label class="block mb-2 text-md text-gray-800 dark:text-neutral-200">
                      Kellele laekub lapsetoetus?
                      <span class="hs-tooltip inline-block align-middle ms-1">
                        <svg class="shrink-0 size-3.5 text-stone-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                          <circle cx="12" cy="12" r="10" />
                          <path d="M12 16v-4" />
                          <path d="M12 8h.01" />
                        </svg>
                        <span class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible opacity-0 transition-opacity hidden invisible z-60 max-w-60 py-1 px-2 bg-stone-900 text-xs font-normal text-white rounded-lg shadow-2xs" role="tooltip">
                          Kellele laekub igakuine lapsetoetus (ja lasterikka pere toetus)?
                        </span>
                      </span>
                    </label>

                    <!-- Select -->
                    <div class="relative">
                      <select name="lapsed_lapsetoetus" data-hs-select='{
        "placeholder": "Vali...",
        "hasSearch": false,
        "searchPlaceholder": "Vali",
        "searchClasses": "block py-1.5 sm:py-2 px-3 w-full sm:text-sm border-gray-200 rounded-lg focus:border-orange-500 focus:ring-orange-500 dark:bg-neutral-950 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder:text-neutral-400",
        "searchWrapperClasses": "bg-white p-2 -mx-1 sticky top-0 dark:bg-neutral-950",
        "toggleTag": "<button type=\"button\" aria-expanded=\"false\"><span class=\"flex\" data-icon></span><span class=\"text-gray-400 dark:text-neutral-200\" data-title></span></button>",
        "toggleClasses": "focus:ring-1 hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 relative py-3 ps-4 pe-9 flex items-center gap-x-2 text-nowrap w-full cursor-pointer bg-white border border-gray-200 rounded-xl text-start text-sm sm:text-base ring-1 ring-transparent hover:border-orange-500 hover:ring-orange-500 focus:outline-hidden focus:ring-orange-500 dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:focus:outline-hidden dark:hover:ring-neutral-600 dark:focus:ring-neutral-600",

        "dropdownClasses": "z-80 max-h-72 p-1 pt-0 space-y-0.5 z-50 w-full overflow-hidden overflow-y-auto bg-white rounded-xl shadow-xl [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500 dark:bg-neutral-950",
        "optionClasses": "hs-selected:bg-gray-100 dark:hs-selected:bg-neutral-800 py-2 px-4 w-full text-sm text-gray-800 cursor-pointer hover:bg-gray-100 rounded-lg focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800",
        "optionTemplate": "<div><div class=\"flex items-center\"><div class=\"shrink-0 me-2\" data-icon></div><div class=\"text-gray-800 dark:text-neutral-200\" data-title></div></div></div>"
      }' class="hidden">
                        <option value="">Vali kellaaeg...</option>
                        <option value="2">Last kasvatavale vanemale</option>
                        <option value="3">Elatist maksvale vanemale</option>
                        <option value="4">Toetust ei maksta</option>
                      </select>

                      <div class="absolute top-1/2 end-2.5 -translate-y-1/2">
                        <svg class="shrink-0 size-3.5 text-gray-500 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                          <path d="m7 15 5 5 5-5" />
                          <path d="m7 9 5-5 5 5" />
                        </svg>
                      </div>
                    </div>
                    <!-- End Select -->
                  </div>


                  <!-- //LINK - Makstud elatis -->
                  <div>
                    <label for="id-elatisvolg-makstud" class="block mb-2 text-md text-gray-800 dark:text-neutral-200">
                      Lapsetoetuse suurus
                      <span class="hs-tooltip inline-block align-middle ms-1">
                        <svg class="shrink-0 size-3.5 text-stone-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                          <circle cx="12" cy="12" r="10" />
                          <path d="M12 16v-4" />
                          <path d="M12 8h.01" />
                        </svg>
                        <span class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible opacity-0 transition-opacity hidden invisible z-60 max-w-60 py-1 px-2 bg-stone-900 text-xs font-normal text-white rounded-lg shadow-2xs" role="tooltip">
                          Kui kui suur on lapsetoetus ja lasterikka pere toetus kuus?
                        </span>
                      </span>
                    </label>
                    <div class="relative">
                      <input name="elatisvolg-makstud" id="id-elatisvolg-makstud" type="text" class="py-3 px-4 block w-full border-gray-200 rounded-xl pe-16 placeholder:text-gray-400 focus:border-orange-500 focus:ring-orange-500 disabled:opacity-50 disabled:pointer-events-none focus:ring-1" placeholder="Sisesta...">
                      <div class="absolute inset-y-0 end-0 flex items-center pointer-events-none z-20 pe-4">
                        <span class="text-md text-gray-500">eurot</span>
                      </div>
                    </div>
                  </div>
                </div>

              </div>
              <!-- //!SECTION - Lapse andmed -->
            </div>

            <!-- //SECTION - ELATISRAHA -->
            <div class="mb-8 pb-8 border-b border-gray-100 last:pb-0 last:mb-0 last:border-b-0 dark:border-neutral-700">
              <div class="space-y-4">
                <h2 class="mb-1 font-medium text-xl text-gray-800 dark:text-neutral-200">
                  Elatisraha
                </h2>
                <p class="pb-1 text-base text-gray-500 dark:text-neutral-400 max-w-md">
                  Sisesta andmed elatise arvutamiseks
                </p>

                <div class="grid md:grid-cols-2 gap-4">

                  <!-- //LINK - Elatisabi -->
                  <div class="mb-3">
                    <label class="block mb-2 text-md text-gray-800 dark:text-neutral-200">
                      Kas soovid nõuda elatisabi?
                    </label>
                    <div class="flex gap-x-6 items-center">
                      <div class="flex items-center">
                        <input name="elatis_elatisabi" id="id-elatis-elatisabi-jah" type="radio" class="shrink-0 mt-0.5 border-gray-200 rounded-full text-orange-500 focus:ring-transparent checked:border-orange-500" value="Jah, soovin">
                        <label for="id-elatis-elatisabi-jah" class="text-md text-gray-500 ms-2">Jah, soovin</label>
                      </div>
                      <div class="flex items-center">
                        <input name="elatis_elatisabi" id="id-elatis-elatisabi-ei" type="radio" class="shrink-0 mt-0.5 border-gray-200 rounded-full text-orange-500 focus:ring-transparent checked:border-orange-500" value="Ei soovi" checked>
                        <label for="id-elatis-elatisabi-ei" class="text-md text-gray-500 ms-2">Ei soovi</label>
                      </div>
                    </div>
                  </div>

                  <!-- //LINK - Elatisvõlg -->
                  <div class="mb-3">
                    <label class="block mb-2 text-md text-gray-800 dark:text-neutral-200">
                      Kas soovid nõuda elatisvõlga?
                    </label>
                    <div class="flex gap-x-6 items-center">
                      <div class="flex items-center">
                        <input name="elatisvolg_noudmine" id="id-elatisvolg-jah" type="radio" class="shrink-0 mt-0.5 border-gray-200 rounded-full text-orange-500 focus:ring-transparent checked:border-orange-500" value="Soovin elatisvõlga" onclick="document.getElementById('id-elatisvolg-lisainfo').classList.remove('hidden')">
                        <label for="id-elatisvolg-jah" class="text-md text-gray-500 ms-2">Jah, soovin</label>
                      </div>
                      <div class="flex items-center">
                        <input name="elatisvolg_noudmine" id="id-elatisvolg-ei" type="radio" class="shrink-0 mt-0.5 border-gray-200 rounded-full text-orange-500 focus:ring-transparent checked:border-orange-500" value="Ei soovi elatisvõlga" checked onclick="document.getElementById('id-elatisvolg-lisainfo').classList.add('hidden')">
                        <label for="id-elatisvolg-ei" class="text-md text-gray-500 ms-2">Ei soovi</label>
                      </div>
                    </div>
                  </div>

                  <!-- Elatisvõla lisainfo -->
                  <div id="id-elatisvolg-lisainfo" class="hidden md:col-span-2">
                    <div class="grid grid-cols-1 md:grid-cols-2 space-y-4 gap-4">

                      <!-- //LINK - Elatisvõla kuupäev -->
                      <div class="">
                        <label for="id-elatisvolg-kuupaev" class="block mb-2 text-md text-gray-800 dark:text-neutral-200">
                          Mis kuupäevast soovid elatisvõlga nõuda?
                          <span class="hs-tooltip inline-block align-middle ms-1">
                            <svg class="shrink-0 size-3.5 text-stone-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                              <circle cx="12" cy="12" r="10" />
                              <path d="M12 16v-4" />
                              <path d="M12 8h.01" />
                            </svg>
                            <span class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible opacity-0 transition-opacity hidden invisible z-60 max-w-60 py-1 px-2 bg-stone-900 text-xs font-normal text-white rounded-lg shadow-2xs" role="tooltip">
                              Elatisvõlga saab nõuda kuni viimase 12 kuu eest.
                            </span>
                          </span>
                        </label>
                        <input name="elatisvolg_kuupaev" id="id-elatisvolg-kuupaev" type="text" class="py-3 px-4 block w-full border-gray-200 rounded-xl placeholder:text-gray-400 focus:border-orange-500 focus:ring-orange-500 disabled:opacity-50 disabled:pointer-events-none focus:ring-1" placeholder="01.01.2025">
                      </div>

                      <!-- //LINK - Makstud elatis -->
                      <div>
                        <label for="id-elatisvolg-makstud" class="block mb-2 text-md text-gray-800 dark:text-neutral-200">
                          Kui palju on vanem elatist kokku maksnud?
                          <span class="hs-tooltip inline-block align-middle ms-1">
                            <svg class="shrink-0 size-3.5 text-stone-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                              <circle cx="12" cy="12" r="10" />
                              <path d="M12 16v-4" />
                              <path d="M12 8h.01" />
                            </svg>
                            <span class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible opacity-0 transition-opacity hidden invisible z-60 max-w-60 py-1 px-2 bg-stone-900 text-xs font-normal text-white rounded-lg shadow-2xs" role="tooltip">
                              Kui palju on vanem alates valitud kuupäevast elatist kokku maksnud?
                            </span>
                          </span>
                        </label>
                        <div class="relative">
                          <input name="elatisvolg-makstud" id="id-elatisvolg-makstud" type="text" class="py-3 px-4 block w-full border-gray-200 rounded-xl pe-16 placeholder:text-gray-400 focus:border-orange-500 focus:ring-orange-500 disabled:opacity-50 disabled:pointer-events-none focus:ring-1" placeholder="300">
                          <div class="absolute inset-y-0 end-0 flex items-center pointer-events-none z-20 pe-4">
                            <span class="text-md text-gray-500">eurot</span>
                          </div>
                        </div>
                      </div>

                    </div>
                  </div>

                </div>

              </div>

            </div>
            <!-- //!SECTION - Elatisraha -->

            <!-- //SECTION - LISAINFO -->
            <div class="pb-8 mb-8 border-b border-gray-100 last:pb-0 last:mb-0 last:border-b-0 dark:border-neutral-700">
              <div class="space-y-4">
                <h2 class="mb-1 font-medium text-xl text-gray-800 dark:text-neutral-200">
                  Lisainfo
                </h2>
                <p class="pb-1 text-base text-gray-500 dark:text-neutral-400 max-w-md">
                  Lisa teave, mis võib olla oluline
                </p>

                <!-- //LINK - Kirjeldus -->
                <div>
                  <label for="id-lisainfo" class="block mb-2 text-md text-gray-800 dark:text-neutral-200">
                    Lisateave
                    <span class="hs-tooltip inline-block align-middle ms-1">
                      <svg class="shrink-0 size-3.5 text-stone-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="12" cy="12" r="10" />
                        <path d="M12 16v-4" />
                        <path d="M12 8h.01" />
                      </svg>
                      <span class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible opacity-0 transition-opacity hidden invisible z-60 max-w-60 py-1 px-2 bg-stone-900 text-xs font-normal text-white rounded-lg shadow-2xs" role="tooltip">
                        Kirjelda enda olukorda ja lisa teave, mis võib olla oluline.
                      </span>
                    </span>
                  </label>
                  <textarea name="klient_lisainfo" id="id-lisainfo" class="p-3 sm:p-4 block w-full border-gray-200 rounded-2xl sm:text-sm focus:ring-1 focus:border-orange-500 focus:ring-orange-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600" placeholder="Mida rohkem infot, seda paremini saame Sind aidata..." rows="5" data-hs-textarea-auto-height></textarea>
                </div>

                <!-- //LINK - Failid -->
                <div class="space-y-2">
                  <label for="id-klient-dokumendid" class="block mb-2 text-md text-gray-800 dark:text-neutral-200">
                    Failid
                    <span class="hs-tooltip inline-block align-middle ms-1">
                      <svg class="shrink-0 size-3.5 text-stone-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="12" cy="12" r="10" />
                        <path d="M12 16v-4" />
                        <path d="M12 8h.01" />
                      </svg>
                      <span class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible opacity-0 transition-opacity hidden invisible z-60 max-w-60 py-1 px-2 bg-stone-900 text-xs font-normal text-white rounded-lg shadow-2xs" role="tooltip">
                        Lisada võib näiteks kohtulahendid, dokumendid, väljavõtted, kirjavahetus
                      </span>
                    </span>
                  </label>
                  <label name="klient_dokumendid" for="id-klient-dokumendid" class="p-6 flex justify-center bg-white border border-dashed border-gray-300 rounded-xl dark:bg-neutral-800 dark:border-neutral-600 cursor-pointer">
                    <div class="text-center">
                      <svg class="w-16 text-gray-400 mx-auto dark:text-neutral-400" width="70" height="46" viewBox="0 0 70 46" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M6.05172 9.36853L17.2131 7.5083V41.3608L12.3018 42.3947C9.01306 43.0871 5.79705 40.9434 5.17081 37.6414L1.14319 16.4049C0.515988 13.0978 2.73148 9.92191 6.05172 9.36853Z" fill="currentColor" stroke="currentColor" stroke-width="2" class="fill-white stroke-gray-400 dark:fill-neutral-800 dark:stroke-neutral-500" />
                        <path d="M63.9483 9.36853L52.7869 7.5083V41.3608L57.6982 42.3947C60.9869 43.0871 64.203 40.9434 64.8292 37.6414L68.8568 16.4049C69.484 13.0978 67.2685 9.92191 63.9483 9.36853Z" fill="currentColor" stroke="currentColor" stroke-width="2" class="fill-white stroke-gray-400 dark:fill-neutral-800 dark:stroke-neutral-500" />
                        <rect x="17.0656" y="1.62305" width="35.8689" height="42.7541" rx="5" fill="currentColor" stroke="currentColor" stroke-width="2" class="fill-white stroke-gray-400 dark:fill-neutral-800 dark:stroke-neutral-500" />
                        <path d="M47.9344 44.3772H22.0655C19.3041 44.3772 17.0656 42.1386 17.0656 39.3772L17.0656 35.9161L29.4724 22.7682L38.9825 33.7121C39.7832 34.6335 41.2154 34.629 42.0102 33.7025L47.2456 27.5996L52.9344 33.7209V39.3772C52.9344 42.1386 50.6958 44.3772 47.9344 44.3772Z" stroke="currentColor" stroke-width="2" class="stroke-gray-400 dark:stroke-neutral-500" />
                        <circle cx="39.5902" cy="14.9672" r="4.16393" stroke="currentColor" stroke-width="2" class="stroke-gray-400 dark:stroke-neutral-500" />
                      </svg>

                      <div class="mt-4 flex flex-wrap justify-center text-sm/6 text-gray-600">
                        <span class="pe-1 font-medium text-gray-800 dark:text-neutral-200">
                          Lisa või lohista siia
                        </span>
                        <span class="relative bg-white font-semibold text-orange-600 hover:text-orange-700 rounded-lg decoration-2 hover:underline focus-within:outline-hidden focus-within:ring-2 focus-within:ring-orange-600 focus-within:ring-offset-2 dark:bg-neutral-800 dark:text-orange-500 dark:hover:text-orange-600">
                          failid
                        </span>
                      </div>

                      <p class="mt-1 text-xs text-gray-400 dark:text-neutral-400">
                        Lisa kõik dokumenid, mis võivad olla olulised
                      </p>
                    </div>
                    <input id="id-klient-dokumendid" name="klient_dokumendid[]" type="file" class="sr-only" multiple accept=".pdf,.doc,.docx,.csv,.jpg,.jpeg,.png">
                  </label>
                </div>

              </div>

            </div>
            <!-- //!SECTION - Lisainfo -->

            <!-- Checkbox -->
            <div class="mt-5 flex items-center">
              <div class="flex">
                <input name="klient_kasutustingimused" id="tingimused" type="checkbox" class="shrink-0 mt-0.5 border-gray-200 rounded-sm text-orange-500 focus:ring-transparent">
              </div>
              <div class="ms-3">
                <label for="tingimused" class="text-sm">Olen tutvunud ja nõustun <a type="button" class="text-orange-500 decoration-1 hover:underline font-medium" aria-haspopup="dialog" aria-expanded="false" aria-controls="hs-vertically-centered-scrollable-modal" data-hs-overlay="#hs-vertically-centered-scrollable-modal" href="#">kasutustingimustega</a>.</label>
              </div>
            </div>
            <!-- End Checkbox -->
            <div id="hs-vertically-centered-scrollable-modal" class="hs-overlay hidden size-full fixed top-0 left-0 z-80 overflow-x-hidden overflow-y-auto pointer-events-none flex items-center justify-center" role="dialog" tabindex="-1" aria-labelledby="hs-vertically-centered-scrollable-modal-label">
              <div class="hs-overlay-open:mt-7 hs-overlay-open:opacity-100 hs-overlay-open:duration-500 mt-0 opacity-0 ease-out transition-all md:max-w-2xl md:w-full m-3 md:mx-auto h-[calc(100%-56px)] min-h-[calc(100%-56px)] flex items-center">
                <div class="w-full max-h-full overflow-hidden flex flex-col bg-white border border-gray-200 shadow-2xs rounded-xl pointer-events-auto">
                  <div class="flex justify-between items-center py-3 px-4 border-b border-gray-200">
                    <h3 id="hs-vertically-centered-scrollable-modal-label" class="font-bold text-gray-800">
                      Alimendid.ee kasutustingimused
                    </h3>
                    <button type="button" class="size-8 inline-flex justify-center items-center gap-x-2 rounded-full border border-transparent bg-gray-100 text-gray-800 hover:bg-gray-200 focus:outline-hidden focus:bg-gray-200 disabled:opacity-50 disabled:pointer-events-none" aria-label="Close" data-hs-overlay="#hs-vertically-centered-scrollable-modal">
                      <span class="sr-only">Sulge</span>
                      <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M18 6 6 18"></path>
                        <path d="m6 6 12 12"></path>
                      </svg>
                    </button>
                  </div>
                  <div class="p-4 overflow-y-auto">
                    <div class="space-y-4">

                      <!-- Üldsätted -->
                      <div>
                        <h3 class="text-lg font-semibold text-gray-800">1. Üldsätted</h3>
                        <p class="mt-1 text-gray-800">
                          1.1. Käesolevad kasutustingimused (edaspidi "Kasutustingimused") reguleerivad internetiaadressil www.alimendid.ee asuva veebilehe (edaspidi "Veebileht") kaudu pakutava õigusabi osutamise teenuse (edaspidi "Teenus") kasutamise tingimusi.
                        </p>
                        <p class="mt-1 text-gray-800">
                          1.2. Veebilehe operaatoriks ja teenuse pakkujaks on Alimendid.ee OÜ (edaspidi "Alimendid.ee"), aadress Maakri 19, Tallinn 10145, äriregistri kood 11269840, e-<NAME_EMAIL>.
                        </p>
                        <p class="mt-1 text-gray-800">
                          1.3. Veebilehe kasutaja (edaspidi "Kasutaja") kinnitab Kasutustingimustega nõustumisel, et kõik tema poolt sisestatud andmed on õiged ja tal on kõik õigused ja volitused Teenuste kasutamiseks.
                        </p>
                      </div>

                      <!-- Teenus -->
                      <div>
                        <h3 class="text-lg font-semibold text-gray-800">2. Teenus</h3>
                        <p class="mt-1 text-gray-800">
                          2.1. Alimendid.ee võimaldab Kasutajal kasutada Veebilehe kaudu erinevaid õigusabi teenuseid, sealhulgas õigusalast nõustamist, dokumentide koostamist ja menetlusabi.
                        </p>
                        <p class="mt-1 text-gray-800">
                          2.2. Teenuse osutamiseks peab Kasutaja täitma Veebilehel ettenähtud kohustuslikud andmeväljad.
                        </p>
                        <p class="mt-1 text-gray-800">
                          2.3. Sõltuvalt valitud paketist on teenustasu suuruseks Veebilehe hinnakirjas avaldatud teenustasud. Kõikide Veebilehel müüdavate teenuste hinnad on avaldatud eurodes.
                        </p>
                        <p class="mt-1 text-gray-800">
                          2.4. Teenuse eest tasumise järel osutab Alimendid.ee lepingu esemeks oleva teenuse Veebilehel avaldatud tingimustes ja tähtaja jooksul.
                        </p>
                        <p class="mt-1 text-gray-800">
                          2.5. Teenustasu saab tasuda turvaliselt läbi Eesti pangamaksete: Swedbank, SEB, Luminor, LHV, Coop Pank, Citadele, Pocopay. Maksete vahendajaks on Maksekeskus AS. Tasumine toimub väljaspool Veebilehte vastava panga turvalises keskkonnas. Alimendid.ee'l puudub ligipääs Kasutaja panga andmetele.
                        </p>
                        <p class="mt-1 text-gray-800">
                          2.6. Alimendid.ee on Kasutaja isikuandmete vastutav töötleja. Alimendid.ee edastab maksete teostamiseks vajalikud isikuandmed volitatud töötleja Maksekeskus AS-ile.
                        </p>
                        <p class="mt-1 text-gray-800">
                          2.7. Pangalingi vahendusel makse sooritamise järel tuleb Kasutajal vajutada nupule "Tagasi kaupmehe juurde", mille järel suunatakse Kasutaja tellimuse kättesaamist kinnitavale veebilehele.
                        </p>
                        <p class="mt-1 text-gray-800">
                          2.8. Tellimuse kinnitamise järel puudub Kasutajal lepingust taganemise õigus (VÕS § 53 lg 4 p 1). Lepingu esemeks oleva teenuse osutamise ja Kasutajale kättesaadavaks tegemisega on Alimendid.ee täitnud täielikult kõik lepingust tulenevad kohustused.
                        </p>
                        <p class="mt-1 text-gray-800">
                          2.9. Veebilehe kaudu õigusabi teenuse kasutamine ei kujuta endast advokaadibüroo teenust ega asenda kohtus esindamist, välja arvatud juhul, kui see on Veebilehel eraldi märgitud.
                        </p>
                      </div>

                      <!-- Isikuandmed -->
                      <div>
                        <h3 class="text-lg font-semibold text-gray-800">3. Isikuandmed</h3>
                        <p class="mt-1 text-gray-800">
                          3.1. Veebilehte ja õigusalase nõustamise sisu puudutavad autori- ja muud õigused kuuluvad Alimendid.ee-le.
                        </p>
                        <p class="mt-1 text-gray-800">
                          3.2. Veebilehe abil koostatud dokumendid ja nende sisu on konfidentsiaalsed. Kasutajal ei ole õigust ilma Alimendid.ee eelneva kirjaliku nõusolekuta avalikult kättesaadavaks teha Alimendid.ee poolt koostatud dokumentide sisu.
                        </p>
                        <p class="mt-1 text-gray-800">
                          3.3. Alimendid.ee töötleb järgnevaid isikuandmeid: ees- ja perekonnanimi, e-posti aadress. Kasutajal on igal ajal õigus tutvuda enda poolt sisestatud andmetega ja nõuda Alimendid.ee-lt kõikide Kasutajat puudutavate ja tema poolt sisestatud andmete kustutamist.
                        </p>
                        <p class="mt-1 text-gray-800">
                          3.4. Alimendid.ee ei edasta, müü ega avalikusta Kasutaja andmeid kolmandatele osapooltele.
                        </p>
                      </div>

                      <!-- Lõppsätted -->
                      <div>
                        <h3 class="text-lg font-semibold text-gray-800">4. Lõppsätted</h3>
                        <p class="mt-1 text-gray-800">
                          4.1. Alimendid.ee-l on õigus Kasutustingimusi igal ajal muuta. Muudetud Kasutustingimused jõustuvad nende avaldamisel Veebilehel.
                        </p>
                        <p class="mt-1 text-gray-800">
                          4.2. Alimendid.ee ei vastuta otseste, kaudsete, karistuslike, faktiliste või kaasuvate kahjude eest, mis tulenevad Teenuste kasutamisest või Veebilehe sisust.
                        </p>
                        <p class="mt-1 text-gray-800">
                          4.3. Kasutustingimustest tulenevatele õigussuhetele kohaldatakse Eesti Vabariigi õigust.
                        </p>
                        <p class="mt-1 text-gray-800">
                          4.4. Võimalikke vaidlusi lahendatakse Harju Maakohtu Tallinna kohtumajas.
                        </p>
                      </div>

                    </div>
                  </div>


                  <div class="flex justify-end items-center gap-x-2 py-3 px-4 border-t border-gray-200">
                    <button id="laadi-alla-tingimused" type="button" class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-2xs hover:bg-gray-50 focus:outline-hidden focus:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none">
                      <svg class="w-4 h-4 mr-1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                        <polyline points="7 10 12 15 17 10"></polyline>
                        <line x1="12" y1="15" x2="12" y2="3"></line>
                      </svg>
                      Laadi alla
                    </button>
                    <button type="button" class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-orange-500 text-white hover:bg-orange-500 focus:outline-hidden focus:bg-orange-500 disabled:opacity-50 disabled:pointer-events-none" data-hs-overlay="#hs-vertically-centered-scrollable-modal">
                      Sulge
                    </button>

                  </div>
                </div>
              </div>
            </div>

            <!-- Hidden field -->
            <input type="hidden" name="step" value="laps">
            <input type="hidden" id="laps_arv" name="laps_arv" value="1">

            <!-- SECTION ‧ NUPUD -->
            <div class="flex flex-col gap-y-2 pt-5">
              <button type="submit" class="py-3 px-3 w-full inline-flex justify-center items-center gap-x-1.5 font-medium rounded-xl border border-transparent bg-orange-500 text-lg text-white disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-[#F86A1A]"
                style="--tw-bg-opacity: 1;"
                onmouseover="this.style.backgroundColor='#F86A1A'"
                onmouseout="this.style.backgroundColor=''">
                Kinnitan tellimuse
              </button>

              <a class="py-3 px-3 w-full inline-flex justify-center items-center gap-x-1.5 font-medium rounded-xl border border-transparent text-orange-500 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-orange-500 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" href="../../../et/teenused/elatise-dokument/1.php">
                <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="m15 18-6-6 6-6" />
                </svg> Tagasi
              </a>
            </div>
            <!-- End NUPUD -->

          </div>
      </form>
      <!-- End Form -->
  </main>
  <!-- ========== END MAIN CONTENT ========== -->

  <!-- //SECTION - SKRIPTID -->
  <!-- JS PLUGINS -->
  <!-- Required plugins -->
  <script src="../../../assets/vendor/preline/dist/index.js?v=3.1.0"></script>
  <!-- Clipboard -->
  <script src="../../../assets/vendor/clipboard/dist/clipboard.min.js"></script>
  <script src="../../../assets/js/hs-copy-clipboard-helper.js"></script>

  <script>
    // Käivita kohe, mitte DOMContentLoaded sündmuse peale ootamine
    (function() {
      function naitaLapseAndmed(lapseArv) {
        // Peida kõik laste vormid alguses
        for (let i = 2; i <= 3; i++) {
          const vorm = document.getElementById(`id-laps${i}-andmed`);
          if (vorm) vorm.style.display = 'none';
        }

        // Näita vajalikud vormid
        for (let i = 1; i <= lapseArv; i++) {
          const vorm = document.getElementById(`id-laps${i}-andmed`);
          if (vorm) vorm.style.display = 'block';
        }
      }

      // Lisa kuularid raadionuppudele
      document.querySelectorAll('input[name="laste-arv"]').forEach(button => {
        button.addEventListener('change', function() {
          const lapseArv = parseInt(this.id.split('-').pop());
          naitaLapseAndmed(lapseArv);
        });
      });
    })();
  </script>

  <script>
    const naidisAndmed = [{
        nimi: "Mari Tamm",
        isikukood: "41002120228"
      },
      {
        nimi: "Liis Kask",
        isikukood: "31111234567"
      },
      {
        nimi: "Jaan Lepp",
        isikukood: "31205123456"
      }
    ];

    let valjadeArv = 0;

    document.querySelector('[data-hs-copy-markup]').addEventListener('click', function() {
      if (valjadeArv < naidisAndmed.length) {
        valjadeArv++;
        const uusVali = document.querySelector('#hs-wrapper-for-copy').lastElementChild;
        const sisendid = uusVali.querySelectorAll('input');

        sisendid[0].placeholder = naidisAndmed[valjadeArv].nimi;
        sisendid[1].placeholder = naidisAndmed[valjadeArv].isikukood;
      }
    });
  </script>

  <script>
    let lapseNumber = 1;
    const maxLapsi = 3;
    const lapseNimed = ['Teine laps', 'Kolmas laps'];

    // Function to update button visibility
    function updateButtonVisibility() {
      const kustutaNupp = document.getElementById('kustuta-laps-nupp');
      const lisaNupp = document.getElementById('lisa-laps-nupp');

      // Näita kustuta nuppu ainult siis, kui on rohkem kui 1 laps
      if (lapseNumber > 1) {
        kustutaNupp.classList.remove('hidden');
        kustutaNupp.style.display = ''; // Eemalda inline display: none
      } else {
        kustutaNupp.classList.add('hidden');
        kustutaNupp.style.display = 'none'; // Lisa inline display: none
      }

      // Keela lisa nupp, kui on maksimaalne arv lapsi
      if (lapseNumber >= maxLapsi) {
        lisaNupp.disabled = true;
      } else {
        lisaNupp.disabled = false;
      }
    }

    // Lisa lapse nupp
    document.getElementById('lisa-laps-nupp').addEventListener('click', function() {
      if (lapseNumber < maxLapsi) {
        lapseNumber++;

        // Update hidden field with the number of children
        document.getElementById('laps_arv').value = lapseNumber;

        const uusLaps = document.createElement('div');
        uusLaps.className = 'space-y-4';
        uusLaps.id = `laps-${lapseNumber}`;
        uusLaps.innerHTML = `
                    <h3 class="text-lg font-medium text-gray-800 dark:text-neutral-200">${lapseNimed[lapseNumber-2]}</h3>

                    <!-- Lapse nimi ja isikukood -->
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      <div>
                        <input type="text" name="lapse_nimi[]" class="py-3 px-4 block w-full border-gray-200 rounded-xl placeholder:text-gray-400 focus:border-orange-500 focus:ring-orange-500 disabled:opacity-50 disabled:pointer-events-none focus:ring-1" placeholder="Nimi">
                      </div>
                      <div>
                        <input type="text" name="lapse_isikukood[]" pattern="[0-9]{11}" maxlength="11" class="py-3 px-4 block w-full border-gray-200 rounded-xl placeholder:text-gray-400 focus:border-orange-500 focus:ring-orange-500 disabled:opacity-50 disabled:pointer-events-none focus:ring-1" placeholder="Isikukood">
                      </div>
                    </div>

                    <!-- Ööpäevade arv -->
                    <div>
                      <label class="block mb-2 text-md text-gray-800 dark:text-neutral-200">
                        Mitu ööpäeva viibib laps teise vanemaga?
                      </label>

                      <!-- Select -->
                      <div class="relative">
                        <select name="lapse_paevade_arv[]" data-hs-select='{
                            "placeholder": "Vali...",
                            "hasSearch": false,
                            "searchPlaceholder": "Vali",
                            "searchClasses": "block py-1.5 sm:py-2 px-3 w-full sm:text-sm border-gray-200 rounded-lg focus:border-orange-500 focus:ring-orange-500 dark:bg-neutral-950 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder:text-neutral-400",
                            "searchWrapperClasses": "bg-white p-2 -mx-1 sticky top-0 dark:bg-neutral-950",
                            "toggleTag": "<button type=\\"button\\" aria-expanded=\\"false\\"><span class=\\"flex\\" data-icon></span><span class=\\"text-gray-400 dark:text-neutral-200\\" data-title></span></button>",
                            "toggleClasses": "focus:ring-1 hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 relative py-3 ps-4 pe-9 flex items-center gap-x-2 text-nowrap w-full cursor-pointer bg-white border border-gray-200 rounded-xl text-start text-sm sm:text-base ring-1 ring-transparent hover:border-orange-500 hover:ring-orange-500 focus:outline-hidden focus:ring-orange-500 dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:focus:outline-hidden dark:hover:ring-neutral-600 dark:focus:ring-neutral-600",
                            "dropdownClasses": "z-80 max-h-72 p-1 pt-0 space-y-0.5 z-50 w-full overflow-hidden overflow-y-auto bg-white rounded-xl shadow-xl [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500 dark:bg-neutral-950",
                            "optionClasses": "hs-selected:bg-gray-100 dark:hs-selected:bg-neutral-800 py-2 px-4 w-full text-sm text-gray-800 cursor-pointer hover:bg-gray-100 rounded-lg focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800",
                            "optionTemplate": "<div><div class=\\"flex items-center\\"><div class=\\"shrink-0 me-2\\" data-icon></div><div class=\\"text-gray-800 dark:text-neutral-200\\" data-title></div></div></div>"
                        }' class="hidden">
                            <option value="">Vali</option>
                            <option value="1">1 ööpäeva</option>
                            <option value="2">2 ööpäeva</option>
                            <option value="3">3 ööpäeva</option>
                            <option value="4">4 ööpäeva</option>
                            <option value="5">5 ööpäeva</option>
                            <option value="6">6 ööpäeva</option>
                            <option value="7">7 ööpäeva</option>
                            <option value="8">8 ööpäeva</option>
                            <option value="9">9 ööpäeva</option>
                            <option value="10">10 ööpäeva</option>
                            <option value="11">11 ööpäeva</option>
                            <option value="12">12 ööpäeva</option>
                            <option value="13">13 ööpäeva</option>
                            <option value="14">14 ööpäeva</option>
                            <option value="15">15 ööpäeva</option>
                        </select>
                        <div class="absolute top-1/2 end-2.5 -translate-y-1/2">
                            <svg class="shrink-0 size-3.5 text-gray-500 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="m7 15 5 5 5-5" />
                                <path d="m7 9 5-5 5 5" />
                            </svg>
                        </div>
                      </div>
                      <!-- End Select -->
                    </div>
            `;

        document.getElementById('lapsed-konteiner').appendChild(uusLaps);

        // Initsialiseerime uue select elemendi
        HSSelect.autoInit();

        // Update button visibility
        updateButtonVisibility();
      }
    });

    // Kustuta lapse nupp
    document.getElementById('kustuta-laps-nupp').addEventListener('click', function() {
      if (lapseNumber > 1) {
        // Remove the last child element
        const lastChild = document.getElementById(`laps-${lapseNumber}`);
        if (lastChild) {
          lastChild.remove();
        }

        lapseNumber--;

        // Update hidden field with the number of children
        document.getElementById('laps_arv').value = lapseNumber;

        // Update button visibility
        updateButtonVisibility();
      }
    });

    // Initsialiseeri nuppude nähtavus lehe laadimisel
    document.addEventListener('DOMContentLoaded', function() {
      updateButtonVisibility();
    });
  </script>

  <!-- //LINK - Laadi alla kasutustingimused -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const laadiAllaNupp = document.getElementById('laadi-alla-tingimused');

      if (laadiAllaNupp) {
        laadiAllaNupp.addEventListener('click', function() {
          const {
            jsPDF
          } = window.jspdf;
          const doc = new jsPDF();

          doc.setFont("helvetica", "bold");
          doc.setFontSize(16);
          doc.text("Alimendid.ee kasutustingimused", 20, 20);

          doc.setFont("helvetica", "normal");
          doc.setFontSize(12);

          const tingimused = document.querySelector('#hs-vertically-centered-scrollable-modal .space-y-4');
          if (tingimused) {
            const blocks = tingimused.children;
            let yPos = 30;

            for (let block of blocks) {
              const titleEl = block.querySelector('h3');
              const paragraphs = block.querySelectorAll('p');

              // Lisa pealkiri
              if (titleEl) {
                if (yPos > 270) {
                  doc.addPage();
                  yPos = 20;
                }

                doc.setFont("helvetica", "bold");
                doc.setFontSize(14);
                doc.text(titleEl.textContent, 20, yPos);
                yPos += 10;
              }

              // Lisa kõik <p>-d
              doc.setFont("helvetica", "normal");
              doc.setFontSize(12);
              for (let p of paragraphs) {
                const text = p.textContent.trim();
                const lines = doc.splitTextToSize(text, 170);

                if (yPos + (lines.length * 7) > 280) {
                  doc.addPage();
                  yPos = 20;
                }

                doc.text(lines, 20, yPos);
                yPos += lines.length * 5 + 2;
              }

              yPos += 5; // lisaruum plokkide vahel
            }
          } else {
            doc.text("Kasutustingimused ei ole saadaval. Palun külastage meie veebilehte.", 20, 30);
          }

          doc.setFont("helvetica", "italic");
          doc.setFontSize(10);
          const tänaneKuupäev = new Date().toLocaleDateString('et-EE');
          doc.text(`Alimendid.ee kasutustingimused seisuga ${tänaneKuupäev}`, 20, 280);

          doc.save("Alimendid_ee_kasutustingimused.pdf");
        });
      }
    });
  </script>

  <!-- //LINK - Failide laadimine -->
  <script src="../../../assets/vendor/lodash/lodash.min.js"></script>
  <script src="../../../assets/vendor/dropzone/dist/dropzone-min.js"></script>
  <!-- //!SECTION - Skriptid -->

</body>

</html>