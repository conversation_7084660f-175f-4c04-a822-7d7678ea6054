<?php
// Dokumendi teenuse andmebaasi funktsioonid

require_once __DIR__ . '/../db_config.php';
require_once __DIR__ . '/../alimendid_kliendid/kliendid_handler.php';
require_once __DIR__ . '/email_templates.php';

/**
 * Salvesta dokumendi teenuse andmed
 */
function save_dokument_data($form_data, $files_data = []) {
    global $pdo;
    
    try {
        // Alusta tehingut
        $pdo->beginTransaction();
        
        // Võta andmed vormist
        $klient_nimi = $form_data['klient_nimi'] ?? '';
        $klient_post = $form_data['klient_post'] ?? '';
        $klient_telefon = $form_data['klient_telefon'] ?? '';
        $klient_lisainfo = $form_data['klient_lisainfo'] ?? '';
        $klient_kasutustingimused = isset($form_data['klient_kasutustingimused']) ? 1 : 0;
        
        // Paketi andmed
        $pakett = $form_data['pakett'] ?? 'dokument-odav';
        $teenus = 'Dokument';
        
        // Määra hind paketi põhjal
        $hinnad = [
            'dokument-odav' => 69.00,
            'dokument-keskmine' => 99.00,
            'dokument-kallis' => 199.00
        ];
        $hind = $hinnad[$pakett] ?? 69.00;
        
        // Lapse andmed
        $lapsed_andmed = [];
        if (isset($form_data['lapse_nimi']) && is_array($form_data['lapse_nimi'])) {
            for ($i = 0; $i < count($form_data['lapse_nimi']); $i++) {
                if (!empty($form_data['lapse_nimi'][$i])) {
                    $lapsed_andmed[] = [
                        'nimi' => $form_data['lapse_nimi'][$i],
                        'isikukood' => $form_data['lapse_isikukood'][$i] ?? '',
                        'paevade_arv' => (int)($form_data['lapse_paevade_arv'][$i] ?? 0)
                    ];
                }
            }
        }
        
        // Teise vanema andmed
        $teine_vanem_nimi = $form_data['teine_vanem_nimi'] ?? '';
        $teine_vanem_isikukood = $form_data['teine_vanem_isikukood'] ?? '';
        $teine_vanem_aadress = $form_data['teine_vanem_aadress'] ?? '';
        $teine_vanem_telefon = $form_data['teine_vanem_telefon'] ?? '';
        $teine_vanem_email = $form_data['teine_vanem_email'] ?? '';
        
        // Elatise andmed
        $elatise_liik = $form_data['elatise_liik'] ?? 'uus_elatis';
        $elatise_summa = (float)($form_data['elatise_summa'] ?? 0);
        $elatise_alus = $form_data['elatise_alus'] ?? '';
        $elatise_pohjendus = $form_data['elatise_pohjendus'] ?? '';
        
        // Sissetulekute andmed
        $klient_sissetulek = (float)($form_data['klient_sissetulek'] ?? 0);
        $klient_sissetuleku_allikas = $form_data['klient_sissetuleku_allikas'] ?? '';
        $teine_vanem_sissetulek = (float)($form_data['teine_vanem_sissetulek'] ?? 0);
        $teine_vanem_sissetuleku_allikas = $form_data['teine_vanem_sissetuleku_allikas'] ?? '';
        
        // Kulutuste andmed
        $lapse_kulutused = (float)($form_data['lapse_kulutused'] ?? 0);
        $kulutuste_kirjeldus = $form_data['kulutuste_kirjeldus'] ?? '';
        
        // Elamise andmed
        $lapse_elamise_koht = $form_data['lapse_elamise_koht'] ?? 'kliendi_juures';
        $hoolduspaevade_arv = (int)($form_data['hoolduspaevade_arv'] ?? 0);
        
        // Varasemad kohtulahendid
        $varasem_kohtulahend = isset($form_data['varasem_kohtulahend']) ? 1 : 0;
        $kohtulahendi_kuupaev = null;
        if (!empty($form_data['kohtulahendi_kuupaev'])) {
            $kohtulahendi_kuupaev = date('Y-m-d', strtotime($form_data['kohtulahendi_kuupaev']));
        }
        $kohtulahendi_sisu = $form_data['kohtulahendi_sisu'] ?? '';
        
        // Failide üleslaadimine
        $uploaded_files = [];
        if (!empty($files_data['klient_dokumendid']['name'][0])) {
            $upload_dir = __DIR__ . '/../../et/teenused/dokument/failid/';
            if (!is_dir($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }
            
            for ($i = 0; $i < count($files_data['klient_dokumendid']['name']); $i++) {
                if ($files_data['klient_dokumendid']['error'][$i] === UPLOAD_ERR_OK) {
                    $filename = '1234-' . time() . '_' . $files_data['klient_dokumendid']['name'][$i];
                    $filepath = $upload_dir . $filename;
                    
                    if (move_uploaded_file($files_data['klient_dokumendid']['tmp_name'][$i], $filepath)) {
                        $uploaded_files[] = $filename;
                    }
                }
            }
        }
        
        // 1. Salvesta üldised kliendi andmed alimendid_kliendid andmebaasi
        $klient_id = save_klient_data($klient_nimi, $klient_post, $teenus, $pakett, $hind);
        
        // 2. Salvesta kõik andmed dokument andmebaasi (nii üldised kui spetsiifilised)
        $stmt = $pdo->prepare("
            INSERT INTO dokument (
                klient_nimi,
                klient_email,
                klient_telefon,
                teenus,
                pakett,
                hind,
                klient_roll,
                staatus,
                klient_lisainfo,
                klient_dokumendid,
                klient_kasutustingimused,
                lapsed_andmed,
                teine_vanem_nimi,
                teine_vanem_isikukood,
                teine_vanem_aadress,
                teine_vanem_telefon,
                teine_vanem_email,
                elatise_liik,
                elatise_summa,
                elatise_alus,
                elatise_pohjendus,
                klient_sissetulek,
                klient_sissetuleku_allikas,
                teine_vanem_sissetulek,
                teine_vanem_sissetuleku_allikas,
                lapse_kulutused,
                kulutuste_kirjeldus,
                lapse_elamise_koht,
                hoolduspaevade_arv,
                varasem_kohtulahend,
                kohtulahendi_kuupaev,
                kohtulahendi_sisu
            ) VALUES (
                :klient_nimi,
                :klient_email,
                :klient_telefon,
                :teenus,
                :pakett,
                :hind,
                :klient_roll,
                :staatus,
                :klient_lisainfo,
                :klient_dokumendid,
                :klient_kasutustingimused,
                :lapsed_andmed,
                :teine_vanem_nimi,
                :teine_vanem_isikukood,
                :teine_vanem_aadress,
                :teine_vanem_telefon,
                :teine_vanem_email,
                :elatise_liik,
                :elatise_summa,
                :elatise_alus,
                :elatise_pohjendus,
                :klient_sissetulek,
                :klient_sissetuleku_allikas,
                :teine_vanem_sissetulek,
                :teine_vanem_sissetuleku_allikas,
                :lapse_kulutused,
                :kulutuste_kirjeldus,
                :lapse_elamise_koht,
                :hoolduspaevade_arv,
                :varasem_kohtulahend,
                :kohtulahendi_kuupaev,
                :kohtulahendi_sisu
            )
        ");
        
        $stmt->execute([
            ':klient_nimi' => $klient_nimi,
            ':klient_email' => $klient_post,
            ':klient_telefon' => $klient_telefon,
            ':teenus' => $teenus,
            ':pakett' => $pakett,
            ':hind' => $hind,
            ':klient_roll' => 'klient',
            ':staatus' => 'ootel',
            ':klient_lisainfo' => $klient_lisainfo,
            ':klient_dokumendid' => json_encode($uploaded_files),
            ':klient_kasutustingimused' => $klient_kasutustingimused,
            ':lapsed_andmed' => json_encode($lapsed_andmed),
            ':teine_vanem_nimi' => $teine_vanem_nimi,
            ':teine_vanem_isikukood' => $teine_vanem_isikukood,
            ':teine_vanem_aadress' => $teine_vanem_aadress,
            ':teine_vanem_telefon' => $teine_vanem_telefon,
            ':teine_vanem_email' => $teine_vanem_email,
            ':elatise_liik' => $elatise_liik,
            ':elatise_summa' => $elatise_summa,
            ':elatise_alus' => $elatise_alus,
            ':elatise_pohjendus' => $elatise_pohjendus,
            ':klient_sissetulek' => $klient_sissetulek,
            ':klient_sissetuleku_allikas' => $klient_sissetuleku_allikas,
            ':teine_vanem_sissetulek' => $teine_vanem_sissetulek,
            ':teine_vanem_sissetuleku_allikas' => $teine_vanem_sissetuleku_allikas,
            ':lapse_kulutused' => $lapse_kulutused,
            ':kulutuste_kirjeldus' => $kulutuste_kirjeldus,
            ':lapse_elamise_koht' => $lapse_elamise_koht,
            ':hoolduspaevade_arv' => $hoolduspaevade_arv,
            ':varasem_kohtulahend' => $varasem_kohtulahend,
            ':kohtulahendi_kuupaev' => $kohtulahendi_kuupaev,
            ':kohtulahendi_sisu' => $kohtulahendi_sisu
        ]);
        
        // Võta dokument tabeli ID
        $elatise_id = $pdo->lastInsertId();

        // Kinnita tehing
        $pdo->commit();

        // Saada kinnituskiri kliendile
        try {
            error_log("Alustame e-kirja saatmist kliendile: {$klient_post}");

            $email_sent = send_dokument_confirmation(
                $klient_nimi,
                $klient_post,
                $pakett,
                $hind
            );

            if ($email_sent) {
                error_log("✅ Dokumendi kinnituskiri edukalt saadetud: {$klient_post}");
            } else {
                error_log("❌ Dokumendi kinnituskirja saatmine ebaõnnestus: {$klient_post}");
            }
        } catch (Exception $e) {
            error_log("❌ E-kirja saatmise viga: " . $e->getMessage());
            error_log("Stack trace: " . $e->getTraceAsString());
            // E-kirja saatmise viga ei tohiks takistada andmete salvestamist
        }

        return [
            'success' => true,
            'message' => 'Andmed edukalt salvestatud',
            'elatise_id' => $elatise_id,
            'klient_id' => $klient_id
        ];

    } catch (Exception $e) {
        // Tühista tehing
        $pdo->rollBack();
        
        error_log("Dokumendi andmete salvestamise viga: " . $e->getMessage());
        error_log("Stack trace: " . $e->getTraceAsString());
        
        return [
            'success' => false,
            'message' => 'Andmete salvestamisel tekkis viga: ' . $e->getMessage()
        ];
    }
}

// Funktsioon andmete lugemiseks kinnitus lehel kuvamiseks
function get_dokument_data($elatise_id) {
    global $pdo;
    
    try {
        // Loe andmed dokument tabelist (kus on kõik andmed)
        $stmt = $pdo->prepare("
            SELECT 
                klient_nimi,
                klient_email,
                klient_telefon,
                teenus,
                pakett,
                hind,
                tellimuse_kuupaev,
                staatus,
                klient_lisainfo,
                klient_dokumendid,
                klient_kasutustingimused,
                lapsed_andmed,
                teine_vanem_nimi,
                teine_vanem_isikukood,
                elatise_liik,
                elatise_summa,
                created_at,
                updated_at
            FROM dokument 
            WHERE id = :elatise_id
        ");
        
        $stmt->execute([':elatise_id' => $elatise_id]);
        return $stmt->fetch();
        
    } catch (Exception $e) {
        error_log("Dokument andmete lugemise viga: " . $e->getMessage());
        return false;
    }
}

// Funktsioon andmete lugemiseks kliendi ID järgi (alimendid_kliendid tabelist)
function get_dokument_by_klient_id($klient_id) {
    global $pdo;
    
    try {
        // Otsi dokument kirjet, mis vastab kliendi andmetele
        $stmt = $pdo->prepare("
            SELECT d.*, k.id as klient_id
            FROM dokument d
            JOIN kliendid k ON (
                k.klient_nimi = d.klient_nimi AND 
                k.klient_post = d.klient_email AND
                k.teenus = d.teenus
            )
            WHERE k.id = :klient_id
            ORDER BY d.created_at DESC
            LIMIT 1
        ");
        
        $stmt->execute([':klient_id' => $klient_id]);
        return $stmt->fetch();
        
    } catch (Exception $e) {
        error_log("Dokument andmete lugemise viga kliendi ID järgi: " . $e->getMessage());
        return false;
    }
}
?>
