<?php

function Toopaevad($paevadeArv) {
    $kuupaev = new DateTime();
    while ($paevadeArv > 0) {
        $kuupaev->modify('+1 day');
        if (!in_array($kuupaev->format('N'), [6, 7])) {
            $paevadeArv--;
        }
    }
    return $kuupaev->format('d.m.Y');
}

$alimendid_teenus = [

    'arvutamine' => [
        'nimi' => 'Arvutamine',
        'hind_kokku' => 29,
        'hind_ettemaks' => 29,
        'hind_hiljem' => 0,
        'hind_makselink' => 'https://payment.maksekeskus.ee/pay/1/link.html?shopId=cb9a5e95-ae8a-428f-a9c2-d768799ddd6f&amount=29&paymentId=Elatise%20arvutamine&locale=et&country=ee',
        'tahtpaev' => Toopaevad(1)
    ],

    'konsultatsioon' => [
        'nimi' => 'Konsultatsioon',
        'hind_kokku' => 39,
        'hind_ettemaks' => 39,
        'hind_hiljem' => 0,
        'hind_makselink' => 'https://payment.maksekeskus.ee/pay/1/link.html?shopId=cb9a5e95-ae8a-428f-a9c2-d768799ddd6f&amount=39&paymentId=Elatise%20n%C3%B5ustamine&locale=et&country=ee',
        'tahtpaev' => Toopaevad(1)
    ],

    'vastus' => [
        'nimi' => 'Vastus',
        'hind_kokku' => 49,
        'hind_ettemaks' => 49,
        'hind_hiljem' => 0,
        'hind_makselink' => 'https://payment.maksekeskus.ee/pay/1/link.html?shopId=cb9a5e95-ae8a-428f-a9c2-d768799ddd6f&amount=49&paymentId=Elatise%20vastus&locale=et&country=ee',
        'tahtpaev' => Toopaevad(1)
    ],

    'dokument' => [
        'dokument-odav' => [
            'nimi' => 'Elatise dokument (odav)',
            'hind_kokku' => 69,
            'hind_ettemaks' => 69,
            'hind_hiljem' => 0,
            'hind_makselink' => 'https://payment.maksekeskus.ee/pay/1/link.html?shopId=cb9a5e95-ae8a-428f-a9c2-d768799ddd6f&amount=69&paymentId=Elatise%20dokument%20odav&locale=et&country=ee',
            'tahtpaev' => Toopaevad(5)
        ],
        'dokument-keskmine' => [
            'nimi' => 'Elatise dokument (keskmine)',
            'hind_kokku' => 99,
            'hind_ettemaks' => 99,
            'hind_hiljem' => 0,
            'hind_makselink' => 'https://payment.maksekeskus.ee/pay/1/link.html?shopId=cb9a5e95-ae8a-428f-a9c2-d768799ddd6f&amount=99&paymentId=Elatise%20dokument%20keskmine&locale=et&country=ee',
            'tahtpaev' => Toopaevad(3)
        ],
        'dokument-kallis' => [
            'nimi' => 'Elatise dokument (kallis)',
            'hind_kokku' => 199,
            'hind_ettemaks' => 199,
            'hind_hiljem' => 0,
            'hind_makselink' => 'https://payment.maksekeskus.ee/pay/1/link.html?shopId=cb9a5e95-ae8a-428f-a9c2-d768799ddd6f&amount=199&paymentId=Elatise%20dokument%20kallis&locale=et&country=ee',
            'tahtpaev' => Toopaevad(3)
        ]
    ],

    'sissenoudmine' => [
        'A' => [
            'nimi' => 'Sissenõudmine',
            'hind_kokku' => 199,
            'hind_ettemaks' => 1,
            'hind_hiljem' => 198,
            'tahtpaev' => Toopaevad(1)
        ],
        'B' => [
            'nimi' => 'Sissenõudmine',
            'hind_kokku' => 249,
            'hind_ettemaks' => 1,
            'hind_hiljem' => 248,
            'tahtpaev' => Toopaevad(1)
        ],
        'C' => [
            'nimi' => 'Sissenõudmine',
            'hind_kokku' => 299,
            'hind_ettemaks' => 0,
            'hind_hiljem' => 299,
            'tahtpaev' => Toopaevad(1)
        ]
    ],

    'oigusabi' => [
        'nimi' => 'Õigusabi',
        'hind_kokku' => 79,
        'hind_ettemaks' => 1,
        'hind_hiljem' => 78,
        'tahtpaev' => Toopaevad(1)
    ]
];
